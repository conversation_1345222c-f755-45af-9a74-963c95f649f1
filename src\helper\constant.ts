export const DURATION = Object.freeze({
  MONTHLY: "monthly",
  WEEKLY: "weekly",
  YEARLY: "yearly",
});

export const EMAILCONFIG = Object.freeze({
  SUPPORT_EMAIL: "<EMAIL>",
  OFFICE_ADDRESS:
    "Suite 302, Level 3/2 Elizabeth Plaza, North Sydney, NSW 2060",
  LINKDIN_URL:
    "https://www.linkedin.com/company/puppiloversapp?trk=public_profile_topcard-current-company&originalSubdomain=au",
  FACEBOOK_URL: "https://www.facebook.com/puppiloversapp/",
  INSTAGRAM_URL: "https://www.instagram.com/puppiloversapp/",
  TWITTER_URL: "https://www.facebook.com/puppiloversapp",
});

export const NOTIFICATIONS = Object.freeze({
  SHIFT_ADD: {
    HEADER: "New Shift Assigned",
    CONTENT: (name: string, start: string, end: string) =>
      `${name} your shift has been added from ${start} to ${end}`,
  },
  SHIFT_UPDATE: {
    HEADER: "Shift Updated",
    CONTENT: (name: string, start: string, end: string) =>
      `${name} your shift has been updated from ${start} to ${end}`,
  },
  SHIFT_DROP: {
    HEADER: "Shift Dropped",
    CONTENT: (name: string, start: string, end: string) =>
      `${name} has dropped their shift from ${start} to ${end}`,
  },
  SHIFT_DROP_REQUEST: {
    HEADER: "Shift Dropped",
    CONTENT: (name: string, start: string, end: string) =>
      `${name} has requested to drop their shift from ${start} to ${end}`,
  },
  SHIFT_DROP_ACCEPT: {
    HEADER: "Shift Dropped",
    CONTENT: (name: string, start: string, end: string) =>
      `${name} your shift drop has been accepted from ${start} to ${end}`,
  },
  SHIFT_DROP_REJECT: {
    HEADER: "Shift Dropped",
    CONTENT: (name: string, start: string, end: string) =>
      `${name} your shift drop has been rejected from ${start} to ${end}`,
  },
  SHIFT_SWAP: {
    HEADER: "Shift Swap",
    CONTENT: (name: string, start: string, end: string, to: string) =>
      `${name} has swapped their shift from ${start} to ${end} with ${to}`,
  },
  SHIFT_SWAP_ACCEPT: {
    HEADER: "Shift Swap",
    CONTENT: (name: string, start: string, end: string, to: string) =>
      `${name} your shift swap has been accepted from ${start} to ${end} with ${to}`,
  },
  SHIFT_SWAP_REJECT: {
    HEADER: "Shift Swap",
    CONTENT: (name: string, start: string, end: string, to: string) =>
      `${name} your shift swap has been rejected from ${start} to ${end} with ${to}`,
  },
});

export const ROLE_PERMISSIONS = {
  NONE: 0,
  VIEW: 1,
  CREATE: 2,
  EDIT: 4,
  DELETE: 8,
}

export const ROLE_CONSTANT = Object.freeze({
  SUPER_ADMIN: "Super Admin",
  ADMIN: "Admin",
  DIRECTOR: "Director",
  HR: "HR",
  AREA_MANAGER: "Area Manager",
  ACCOUNTANT: "Accountant",
  BRANCH_MANAGER: "Branch Manager",
  ASSIGN_BRANCH_MANAGER: "Assist. Branch Manager",
  HEAD_CHEF: "Head Chef",
  BAR_MANAGER: "Bar Manager",
  FOH: "FOH",
  BAR: "Bar",
  KITCHEN: "Kitchen",
  HOTEL_MANAGER: "Hotel Manager",
  ASSIGN_HOTEL_MANAGER: "Assist. Hotel Manager",
  RECEPTIONIST: "Receptionist",
  HEAD_HOUSEKEEPER: "Head Housekeeper",
  HOUSE_KEEPER: "House Keeper",
  SIGNATURE: "Signature",
});

export const ADMIN_SIDE_USER = [
  ROLE_CONSTANT.SUPER_ADMIN,
  ROLE_CONSTANT.ADMIN,
  ROLE_CONSTANT.DIRECTOR,
  ROLE_CONSTANT.ACCOUNTANT,
  ROLE_CONSTANT.HR,
  ROLE_CONSTANT.AREA_MANAGER,
  ROLE_CONSTANT.BRANCH_MANAGER,
  ROLE_CONSTANT.HOTEL_MANAGER,
  ROLE_CONSTANT.SIGNATURE,
];

export const NORMAL_USER = [
  ROLE_CONSTANT.SUPER_ADMIN,
  ROLE_CONSTANT.ADMIN,
  ROLE_CONSTANT.BRANCH_MANAGER,
  ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER,
  ROLE_CONSTANT.HEAD_CHEF,
  ROLE_CONSTANT.BAR_MANAGER,
  ROLE_CONSTANT.FOH,
  ROLE_CONSTANT.BAR,
  ROLE_CONSTANT.KITCHEN,
  ROLE_CONSTANT.HOTEL_MANAGER,
  ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER,
  ROLE_CONSTANT.RECEPTIONIST,
  ROLE_CONSTANT.HEAD_HOUSEKEEPER,
  ROLE_CONSTANT.HOUSE_KEEPER,
];

export const RABBITMQ_QUEUE = {
  SHIFT_NOTIFICATION: "shift_notification",
  NOTIFICATION_SUCCESS: "notification_success",
};
