import { celebrate, Joi, Segments } from "celebrate";
import { VALID_SHIFT_SETTING_KEYS } from "../models/shiftSettings";

/**
 * Validator for getting shift settings
 */
const getShiftSettingsValidator = celebrate({
  [Segments.QUERY]: Joi.object().keys({
    branch_id: Joi.number().integer().min(1).optional()
      .messages({
        'number.base': 'Branch ID must be a number',
        'number.integer': 'Branch ID must be an integer',
        'number.min': 'Branch ID must be a positive integer'
      })
  })
});

/**
 * Validator for updating shift settings
 */
const updateShiftSettingsValidator = celebrate({
  [Segments.BODY]: Joi.object().keys({
    branch_id: Joi.number().integer().min(1).optional().allow(null)
      .messages({
        'number.base': 'Branch ID must be a number',
        'number.integer': 'Branch ID must be an integer',
        'number.min': 'Branch ID must be a positive integer'
      }),
    settings: Joi.object()
      .pattern(
        Joi.string().valid(...VALID_SHIFT_SETTING_KEYS),
        Joi.boolean()
      )
      .required()
      .min(1)
      .messages({
        'object.base': 'Settings must be an object',
        'object.min': 'At least one setting must be provided',
        'any.required': 'Settings object is required',
        'object.unknown': `Setting key must be one of: ${VALID_SHIFT_SETTING_KEYS.join(', ')}`,
        'boolean.base': 'Setting value must be a boolean'
      })
  })
});

/**
 * Validator for deleting a shift setting
 */
const deleteShiftSettingValidator = celebrate({
  [Segments.PARAMS]: Joi.object().keys({
    key: Joi.string().valid(...VALID_SHIFT_SETTING_KEYS).required()
      .messages({
        'any.only': `Setting key must be one of: ${VALID_SHIFT_SETTING_KEYS.join(', ')}`,
        'any.required': 'Setting key is required'
      })
  }),
  [Segments.QUERY]: Joi.object().keys({
    branch_id: Joi.number().integer().min(1).optional()
      .messages({
        'number.base': 'Branch ID must be a number',
        'number.integer': 'Branch ID must be an integer',
        'number.min': 'Branch ID must be a positive integer'
      })
  })
});

/**
 * Validator for resetting shift settings
 */
const resetShiftSettingsValidator = celebrate({
  [Segments.QUERY]: Joi.object().keys({
    branch_id: Joi.number().integer().min(1).optional()
      .messages({
        'number.base': 'Branch ID must be a number',
        'number.integer': 'Branch ID must be an integer',
        'number.min': 'Branch ID must be a positive integer'
      })
  })
});

export {
  getShiftSettingsValidator,
  updateShiftSettingsValidator,
  deleteShiftSettingValidator,
  resetShiftSettingsValidator,
};
