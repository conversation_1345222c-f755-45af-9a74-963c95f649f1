import { Model, DataTypes } from "sequelize";
import { sequelize } from ".";

interface IShiftHistory {
  id?: number;
  shiftId: number;
  userId: number;
  action:
  | "CREATE"
  | "UPDATE"
  | "DELETE"
  | "PUBLISH"
  | "UNPUBLISH"
  | "SWAP"
  | "DROP"
  | "OPEN"
  | "PRIVATE";
  changes: {
    field: string;
    oldValue: any;
    newValue: any;
  }[];
  metadata: {
    ip: string;
    userAgent: string;
    timestamp: Date;
  };
}

class ShiftHistory extends Model<IShiftHistory> implements IShiftHistory {
  public id!: number;
  public shiftId!: number;
  public userId!: number;
  public action!:
    | "CREATE"
    | "UPDATE"
    | "DELETE"
    | "PUBLISH"
    | "UNPUBLISH"
    | "SWAP"
    | "DROP"
    | "OPEN"
    | "PRIVATE";
  public changes!: {
    field: string;
    oldValue: any;
    newValue: any;
  }[];
  public metadata!: {
    ip: string;
    userAgent: string;
    timestamp: Date;
  };

  // Static method to log shift history
  static async logChange(
    shiftId: number,
    userId: number,
    action:
      | "CREATE"
      | "UPDATE"
      | "DELETE"
      | "PUBLISH"
      | "UNPUBLISH"
      | "SWAP"
      | "DROP"
      | "OPEN"
      | "PRIVATE",
    changes: Array<{ field: string; oldValue: any; newValue: any }>,
    metadata: { ip: string; userAgent: string },
  ) {
    return await ShiftHistory.create({
      shiftId,
      userId,
      action,
      changes,
      metadata: {
        ...metadata,
        timestamp: new Date(),
      },
    });
  }

  // Method to compare and generate changes
  static generateChanges(oldShift: any, newShift: any) {
    const changes = [];
    const fieldsToTrack = [
      "userId",
      "startTime",
      "endTime",
      "status",
      "minutesBreak",
      "branchId",
      "departmentId",
      "roleId",
      "isOpen",
      "isPublished",
      "isDropped",
      "isSwap",
      "acknowledged",
      "createdBy",
      "updatedBy",
      "notes",
    ];

    for (const field of fieldsToTrack) {
      if (
        typeof newShift[field] != "undefined" &&
        `${oldShift[field]}` !== `${newShift[field]}`
      ) {
        changes.push({
          field,
          oldValue: oldShift[field],
          newValue: newShift[field],
        });
      }
    }

    return changes;
  }
}

//   export const initShiftHistory = (sequelize: any) => {
ShiftHistory.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    shiftId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    action: {
      type: DataTypes.ENUM(
        "CREATE",
        "UPDATE",
        "DELETE",
        "PUBLISH",
        "UNPUBLISH",
        "SWAP",
        "DROP",
        "OPEN",
        "PRIVATE",
      ),
      allowNull: false,
    },
    changes: {
      type: DataTypes.JSON,
      allowNull: false,
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "ShiftHistory",
    tableName: "shift_histories",
    timestamps: true,
  },
);

export default ShiftHistory;
//   };

//   export default ShiftHistory;
