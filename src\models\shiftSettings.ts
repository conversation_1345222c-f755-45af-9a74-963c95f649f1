import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

interface ShiftSettingsAttributes {
  id: number;
  branch_id: number | null;
  key: string;
  value: string;
  organization_id: string;
  createdBy: number;
  updatedBy: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export class ShiftSettings extends Model<ShiftSettingsAttributes, never> implements ShiftSettingsAttributes {
  id!: number;
  branch_id!: number | null;
  key!: string;
  value!: string;
  organization_id!: string;
  createdBy!: number;
  updatedBy!: number;
  createdAt!: Date;
  updatedAt!: Date;
}

ShiftSettings.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    branch_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    key: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    value: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updatedBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
    },
    updatedAt: {
      type: DataTypes.DATE,
    },
  },
  {
    sequelize,
    modelName: "ShiftSettings",
    tableName: "shift_settings",
    timestamps: true,
  }
);

// Define valid keys for shift settings
export const VALID_SHIFT_SETTING_KEYS = [
  "allow_weekly_hours_exceed",
  "allow_non_preferred_days",
  "allow_unavailable_users",
  "allow_already_assigned",
  "allow_shift_type_mismatch",
  "allow_leave_users",
  "allow_no_availability_record"
];

// Default values for shift settings
export const DEFAULT_SHIFT_SETTINGS = {
  allow_weekly_hours_exceed: false,
  allow_non_preferred_days: false,
  allow_unavailable_users: false,
  allow_already_assigned: false,
  allow_shift_type_mismatch: false,
  allow_leave_users: false,
  allow_no_availability_record: false
};
