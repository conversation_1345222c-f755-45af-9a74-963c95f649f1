import { Model, DataTypes } from 'sequelize';
import { sequelize } from './index';

interface DropAttributes {
    id: number;
    shiftId: number;
    reason: string;
    status: string;
    createdBy: number;
    updatedBy: number;
    createdAt?: Date;
    updatedAt?: Date;
    organization_id?: string;
}

export class Drop extends Model<DropAttributes, never> implements DropAttributes {
    id!: number;
    shiftId!: number;
    reason!: string;
    status!: string;
    createdBy!: number;
    updatedBy!: number;
    createdAt!: Date;
    updatedAt!: Date;
    organization_id?: string;
}

Drop.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        shiftId: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        reason: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        status: {
            type: DataTypes.ENUM("active", "pending", "deleted"),
            allowNull: false,
            defaultValue: 'pending',
        },
        createdBy: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        updatedBy: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        organization_id: {
            type: DataTypes.STRING,
            allowNull: true,
        },
    },
    {
        sequelize,
        modelName: 'Drop',
        tableName: 'shift_drops',
        timestamps: true,
    }
);

