import { Model, DataTypes } from "sequelize";
import { sequelize } from ".";

export enum AvailabilityStatus {
  active = "active",
  pending = "pending",
  deleted = "deleted",
}

export enum AvailabilityType {
  FULL = "full",
  HALF = "half",
  CUSTOM = "custom",
}

interface AvailabilityAttributes {
  id?: number;
  date: Date;
  type: string;
  userId: number;
  status: string;
  available: boolean;
  timeZone?: string;
  organization_id?: string;
}

export class Availability
  extends Model<AvailabilityAttributes, never>
  implements AvailabilityAttributes
{
  id!: number;
  date!: Date;
  type!: string;
  userId!: number;
  status!: string;
  available!: boolean;
  timeZone?: string;
  organization_id?: string;
}

Availability.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    date: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    type: {
      type: DataTypes.ENUM(Object.values(AvailabilityType)),
      allowNull: false,
      defaultValue: AvailabilityType.FULL,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM(Object.values(AvailabilityStatus)),
      allowNull: false,
      defaultValue: AvailabilityStatus.active,
    },
    available: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    timeZone: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  { sequelize, modelName: "availability" },
);
