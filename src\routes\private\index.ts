import express, { Router } from "express";

const routes: Router = express.Router();

import shiftRoute from "./shifts.routes";
import dayOffRoute from "./day-off.routes";
import availibilityRoute from "./availability.routes";
import shiftSettingsRoute from "./shiftSettings.routes";
import migrationRoute from "./migration.routes";

routes.use("/shifts", shiftRoute);
routes.use("/day-offs", dayOffRoute);
routes.use("/availability", availibilityRoute);
routes.use("/shift-settings", shiftSettingsRoute);
routes.use("/migration", migrationRoute);

export default routes;
