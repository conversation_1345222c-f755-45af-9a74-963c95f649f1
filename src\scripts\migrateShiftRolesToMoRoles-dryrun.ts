/**
 * Dry Run Script: Analyze Shift Role Migration to mo_roles
 * 
 * This script performs a dry-run analysis of the migration without making any changes.
 * It provides detailed reports on what would be migrated and potential issues.
 * 
 * Usage: npx ts-node src/scripts/migrateShiftRolesToMoRoles-dryrun.ts
 */

import { QueryTypes } from 'sequelize';
import { sequelize } from '../models';
import * as dotenv from 'dotenv';

// Import functions and types from main migration script
import {
  createRoleMapping,
  getUserRoleInfo,
  analyzeShifts,
  determineCorrectMoRoleId
} from './migrateShiftRolesToMoRoles';

dotenv.config();

// TypeScript interfaces
interface AnalysisStats {
  totalShifts: number;
  shiftsToMigrate: number;
  shiftsAlreadyCorrect: number;
  shiftsWithIssues: number;
  organizationsAffected: Set<string>;
  rolesAffected: Set<number>;
  migrationReasons: {
    role_mapping: number;
    user_role_validation: number;
    no_mapping_found: number;
    user_no_role_id: number;
  };
}

interface Issue {
  shiftId: number;
  issue: string;
  details: string;
}

interface MigrationPlanItem {
  shiftId: number;
  organizationId: string;
  currentRoleId: number;
  newRoleId: number;
  currentRoleName: string;
  userId: number | null;
  reason: string;
}

interface RoleMappingSummary {
  [roleName: string]: {
    nvRoleId: number;
    organizations: Array<{
      orgId: string;
      moRoleId: number;
    }>;
  };
}

interface AnalysisResult {
  stats: AnalysisStats;
  issues: Issue[];
  migrationPlan: MigrationPlanItem[];
  roleMapping: { [nvRoleId: number]: { [orgId: string]: number } };
  userRoles: any[];
}

// Using existing sequelize connection from models

/**
 * Generate detailed migration report
 */
async function generateMigrationReport(): Promise<AnalysisResult> {
  console.log('📊 Generating Migration Analysis Report...\n');
  
  try {
    // Get all necessary data
    const { roleMapping, mappingResults } = await createRoleMapping();
    const userRoles = await getUserRoleInfo();
    const shifts = await analyzeShifts();
    
    // Analysis counters
    const stats: AnalysisStats = {
      totalShifts: shifts.length,
      shiftsToMigrate: 0,
      shiftsAlreadyCorrect: 0,
      shiftsWithIssues: 0,
      organizationsAffected: new Set(),
      rolesAffected: new Set(),
      migrationReasons: {
        role_mapping: 0,
        user_role_validation: 0,
        no_mapping_found: 0,
        user_no_role_id: 0
      }
    };
    
    const issues: Issue[] = [];
    const migrationPlan: MigrationPlanItem[] = [];
    
    console.log('🔍 Analyzing each shift...\n');
    
    for (const shift of shifts) {
      const newMoRoleId = determineCorrectMoRoleId(shift, roleMapping, userRoles);
      
      stats.organizationsAffected.add(shift.organization_id);
      stats.rolesAffected.add(shift.current_nv_role_id);
      
      if (!newMoRoleId) {
        stats.shiftsWithIssues++;
        stats.migrationReasons.no_mapping_found++;
        issues.push({
          shiftId: shift.shift_id,
          issue: 'No mo_role mapping found',
          details: `nv_role_id: ${shift.current_nv_role_id}, org: ${shift.organization_id}`
        });
      } else if (newMoRoleId === shift.current_nv_role_id) {
        stats.shiftsAlreadyCorrect++;
      } else {
        stats.shiftsToMigrate++;
        
        // Determine migration reason
        let reason = 'role_mapping';
        if (shift.shift_user_id) {
          const userHasRole = userRoles.some(ur => 
            ur.user_id === shift.shift_user_id && 
            ur.nv_role_id === shift.current_nv_role_id &&
            ur.organization_id === shift.organization_id
          );
          
          if (!userHasRole && shift.user_mo_role_id) {
            reason = 'user_role_validation';
          } else if (!userHasRole && !shift.user_mo_role_id) {
            reason = 'user_no_role_id';
            stats.migrationReasons.user_no_role_id++;
          }
        }
        
        stats.migrationReasons[reason as keyof typeof stats.migrationReasons]++;
        
        migrationPlan.push({
          shiftId: shift.shift_id,
          organizationId: shift.organization_id,
          currentRoleId: shift.current_nv_role_id,
          newRoleId: newMoRoleId,
          currentRoleName: shift.current_role_name,
          userId: shift.shift_user_id,
          reason: reason
        });
      }
    }
    
    // Print summary report
    console.log('📋 MIGRATION ANALYSIS SUMMARY');
    console.log('='.repeat(50));
    console.log(`Total Shifts Analyzed: ${stats.totalShifts}`);
    console.log(`Shifts Requiring Migration: ${stats.shiftsToMigrate}`);
    console.log(`Shifts Already Correct: ${stats.shiftsAlreadyCorrect}`);
    console.log(`Shifts with Issues: ${stats.shiftsWithIssues}`);
    console.log(`Organizations Affected: ${stats.organizationsAffected.size}`);
    console.log(`Unique Roles Involved: ${stats.rolesAffected.size}`);
    
    console.log('\n📊 MIGRATION REASONS BREAKDOWN');
    console.log('='.repeat(50));
    console.log(`Role Mapping: ${stats.migrationReasons.role_mapping}`);
    console.log(`User Role Validation: ${stats.migrationReasons.user_role_validation}`);
    console.log(`No Mapping Found: ${stats.migrationReasons.no_mapping_found}`);
    console.log(`User No Role ID: ${stats.migrationReasons.user_no_role_id}`);
    
    // Print role mapping summary
    console.log('\n🗺️  ROLE MAPPING SUMMARY');
    console.log('='.repeat(50));
    const roleMappingSummary: RoleMappingSummary = {};
    mappingResults.forEach(mapping => {
      if (!roleMappingSummary[mapping.role_name]) {
        roleMappingSummary[mapping.role_name] = {
          nvRoleId: mapping.nv_role_id,
          organizations: []
        };
      }
      roleMappingSummary[mapping.role_name].organizations.push({
        orgId: mapping.organization_id,
        moRoleId: mapping.mo_role_id
      });
    });
    
    Object.entries(roleMappingSummary).forEach(([roleName, data]) => {
      console.log(`${roleName} (nv_role_id: ${data.nvRoleId})`);
      data.organizations.forEach(org => {
        console.log(`  └─ Org ${org.orgId}: mo_role_id ${org.moRoleId}`);
      });
    });
    
    // Print issues if any
    if (issues.length > 0) {
      console.log('\n⚠️  ISSUES FOUND');
      console.log('='.repeat(50));
      issues.slice(0, 10).forEach(issue => {
        console.log(`Shift ${issue.shiftId}: ${issue.issue} (${issue.details})`);
      });
      if (issues.length > 10) {
        console.log(`... and ${issues.length - 10} more issues`);
      }
    }
    
    // Print sample migration plan
    if (migrationPlan.length > 0) {
      console.log('\n📝 SAMPLE MIGRATION PLAN (First 10 entries)');
      console.log('='.repeat(80));
      console.log('Shift ID | Org ID | Current Role | New Role | Reason');
      console.log('-'.repeat(80));
      
      migrationPlan.slice(0, 10).forEach(plan => {
        const currentRole = `${plan.currentRoleName || 'Unknown'}(${plan.currentRoleId})`;
        const reason = plan.reason.replace('_', ' ');
        console.log(`${plan.shiftId.toString().padEnd(8)} | ${plan.organizationId.toString().padEnd(6)} | ${currentRole.padEnd(15)} | ${plan.newRoleId.toString().padEnd(8)} | ${reason}`);
      });
      
      if (migrationPlan.length > 10) {
        console.log(`... and ${migrationPlan.length - 10} more migrations planned`);
      }
    }
    
    // Organization breakdown
    console.log('\n🏢 ORGANIZATION BREAKDOWN');
    console.log('='.repeat(50));
    const orgBreakdown: { [orgId: string]: number } = {};
    migrationPlan.forEach(plan => {
      if (!orgBreakdown[plan.organizationId]) {
        orgBreakdown[plan.organizationId] = 0;
      }
      orgBreakdown[plan.organizationId]++;
    });
    
    Object.entries(orgBreakdown)
      .sort(([,a], [,b]) => b - a)
      .forEach(([orgId, count]) => {
        console.log(`Organization ${orgId}: ${count} shifts to migrate`);
      });
    
    console.log('\n✅ DRY RUN ANALYSIS COMPLETE');
    console.log('\nTo proceed with the actual migration, run:');
    console.log('npx ts-node src/scripts/migrateShiftRolesToMoRoles.ts');
    
    return {
      stats,
      issues,
      migrationPlan,
      roleMapping,
      userRoles
    };
    
  } catch (error) {
    console.error('❌ Dry run analysis failed:', (error as Error).message);
    throw error;
  }
}

/**
 * Main dry run function
 */
async function main(): Promise<void> {
  try {
    console.log('🧪 Starting Dry Run Analysis for Shift Role Migration...\n');
    console.log('✅ Using existing database connection\n');

    // Generate report
    await generateMigrationReport();

  } catch (error) {
    console.error('❌ Dry run failed:', (error as Error).message);
    process.exit(1);
  }
}

// Run the dry run
if (require.main === module) {
  main().catch(console.error);
}

export {
  main,
  generateMigrationReport
};
