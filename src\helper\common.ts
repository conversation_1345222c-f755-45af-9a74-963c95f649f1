// import { User } from "../models/User";
import { DURATION, RABBITMQ_QUEUE, ROLE_CONSTANT } from "./constant";
import { getUserRoles } from "../keycloak/common";
import fs from "fs";
import { User } from "../models/User";
import { QueryTypes, sequelize } from "../models";
import rabbitmq from "../rabbitmq/rabbitmq";
import _ from "lodash";

// User status constants for validation
const user_status = {
  PENDING: 'pending',
  DELETED: 'deleted',
  CANCELLED: 'cancelled'
};

/** read html file */
const readHTMLFile = function (path: any, cb: any) {
  /** Read the HTML file at the specified path using UTF-8 encoding */
  fs.readFile(path, "utf-8", function (err, data) {
    /** If an error occurs during reading, log the error and throw it */
    if (err) {
      console.log(err);
      throw err; // Stop the execution and throw the error
    } else {
      /** If no error, pass the file content to the callback function */
      cb(null, data); // call the callback function with the file data
    }
  });
};

/** Get UserId from User table */
const getUserIdFromUserTable = async (userId: any) => {
  try {
    const getUser: any = await User.findOne({
      where: {
        keycloak_userId: userId,
      },
      raw: true,
    });
    if (!getUser) {
      return { status: false, message: "ERROR_USER_NOT_FOUND_IN_KEYCLOAK" };
    }
    return { status: true, data: getUser };
  } catch (e) {
    console.log("Exception: ", e);
    return null;
  }
};

const getPagination = (page: number, size: number) => {
  const limit = size;
  const Page = page || 1;
  const offset = (Page - 1) * limit;
  return { limit, offset };
};

const getPaginatedItems = (
  pageSize: number,
  pageNumber: number,
  total: number,
) => {
  return {
    pageNumber: pageNumber,
    per_page: pageSize,
    total: total,
    total_pages: Math.ceil(total / pageSize),
  };
};

const getDurationBasedOnDays = (days: number) => {
  if (days <= 7) {
    return DURATION.WEEKLY; // Store as weekly for 7 days or less
  } else if (days < 365) {
    return DURATION.MONTHLY; // Store as monthly for days greater than 7 but less than 365
  } else {
    return DURATION.YEARLY; // Store as yearly for 365 days or more
  }
};

/** Check user role status and return response accordingly. */
const checkUserRole = async (userId: any, token: any) => {
  try {
    const getRoles: any = await getUserRoles(userId, token);
    const masterRole: any = getRoles.data.realmMappings.find(
      (role: any) =>
        role.name === global.config.KEYCLOAK_SUPER_ADMIN_ROLE &&
        role.description ===
        global.config.KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION,
    );

    if (!masterRole) {
      return false;
    }
    return true;
  } catch (e) {
    console.log("user role status Exception: ", e);
    return { status: false, message: e };
  }
};

const getUserDeviceId = async (users: any) => {
  try {
    const deviceIds = users.map((user: any) => {
      if (user.appToken && !user.webAppToken) {
        return user.appToken;
      } else if (!user.appToken && user.webAppToken) {
        return user.webAppToken;
      } else if ((user.webAppToken, user.appToken)) {
        return [user.webAppToken, user.appToken];
      } else {
        return [];
      }
    });
    return _.flatten(deviceIds);
  } catch (error) {
    console.log("error", error);
    return false;
  }
};

const getUser = async (id: any, isAuth: boolean = false) => {
  const findUser = await sequelize.query(
    `SELECT id, employment_number, user_first_name, user_middle_name, user_last_name, user_email, branch_id, department_id, IF((user_avatar IS NOT NULL AND user_avatar != ''), CONCAT('${global.config.API_UPLOAD_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar)), '') AS user_avatar_link, user_avatar, user_status, user_active_role_id, web_user_active_role_id, webAppToken, appToken, concat(user_first_name, " ", user_last_name) as user_full_name, rota_group_by, list_order, organization_id FROM nv_users WHERE ${isAuth ? `keycloak_auth_id='${id}'` : `id = ${id}`} AND user_status NOT IN ('cancelled', 'deleted')`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    },
  );

  return findUser && findUser.length > 0 ? findUser[0] : null;
};

const getUsers = async (ids: any) => {
  const findUsers = await sequelize.query(
    `SELECT id, employment_number, user_first_name, user_middle_name, user_last_name, user_email, branch_id, department_id, IF((user_avatar IS NOT NULL AND user_avatar != ''), CONCAT('${global.config.API_UPLOAD_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar)), '') AS user_avatar_link, user_avatar, user_status, user_active_role_id, web_user_active_role_id, webAppToken, appToken, concat(user_first_name, " ", user_last_name) as user_full_name, rota_group_by, list_order, organization_id FROM nv_users WHERE id IN (${ids.join(",")})`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    },
  );
  return findUsers;
};

const getRoles = async (id: any) => {
  const findRoles = await sequelize.query(
    `SELECT * FROM nv_roles WHERE id IN (${id?.join(",")})`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    },
  );
  return findRoles;
};

const getBranchDetails = async (id: any) => {
  const findBranches = await sequelize.query(
    `SELECT * FROM nv_branches WHERE id IN (${id?.join(",")})`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    },
  );
  return findBranches;
};

const getDepartmentDetails = async (id: any) => {
  const findBranches = await sequelize.query(
    `SELECT * FROM nv_departments WHERE id IN (${id?.join(",")})`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    },
  );
  return findBranches;
};

const getUserAllRoles = async (userId: number) => {
  const userRoles = await sequelize.query(
    `SELECT r.id, r.role_name FROM nv_user_roles ur INNER JOIN nv_roles r ON ur.role_id = r.id WHERE ur.user_id = ${userId}`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    },
  );
  return userRoles;
};

const getUserSession = async (token: string, deviceType: string) => {
  // const userSession = await UserSession.findOne({ where: { token, device_type: deviceType } });
  const userSession = await sequelize.query(
    `SELECT * FROM nv_user_session WHERE token = '${token}' AND device_type = '${deviceType}'`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    },
  );
  return userSession && userSession.length > 0 ? userSession[0] : null;
};

const createNotification = async (data: any, req: any, deviceId: any) => {
  // await sequelize.query(
  //   `INSERT INTO notifications (${Object.keys(data).join(",")}) VALUES (${Object.values(
  //     data,
  //   )
  //     .map((value) => `'${value}'`)
  //     .join(",")}) `,
  //   {
  //     type: QueryTypes.INSERT,
  //     returning: true,
  //   },
  // );
  try {
    let notificationObj: any = {
      title: data?.notification_subject,
      description: data.notification_content,
      redirection_object: data?.redirection_object ? JSON.stringify(data?.redirection_object) : null,
      notification_image: null,
      from_user_id: data?.from_user_id,
      to_user_id: data?.to_user_id,
      notification_status: 'sent',
      notification_type: data?.notification_type,
      redirection_type: data?.redirection_type || null,
      reference_id: data?.reference_id || null,
      created_by: req && req.user ? req.user.id : req,
      updated_by: req && req.user ? req.user.id : req,
      notification_meta_id: null,
      deviceId: deviceId
    }

    notificationObj = await addNotificationPreferences(notificationObj)

    await rabbitmq.publishMessage(
      RABBITMQ_QUEUE.SHIFT_NOTIFICATION,
      notificationObj,
    );

  } catch (error) {
    console.log("error", error);
    return false;
  }
};

// Cache for notification preferences to avoid repeated database queries
const notificationPreferencesCache = new Map<string, { email: boolean, push: boolean, banner: boolean, timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

/** Helper function to check notification preferences and add allowed flags - OPTIMIZED */
const addNotificationPreferences = async (templateData: any) => {
  try {
    // Fast extraction of organization_id with priority order
    // Priority: direct field → ORGANIZATION_ID → logo URL → email lookup → deviceId user lookup
    const organization_id = templateData.organization_id ||
      templateData.ORGANIZATION_ID ||
      await getOrganizationIdFromEmail(templateData.email) ||
      await getOrganizationIdFromDeviceId(templateData.deviceId) ||
      extractOrgIdFromLogo(templateData.ORGANIZATION_LOGO);

    if (!organization_id) {
      console.warn('No organization_id found for notification preferences, allowing all notifications');
      return {
        ...templateData,
        allowed: { email: true, push: true, banner: true }
      };
    }

    // Define functionalities that are always allowed (based on the image)
    const alwaysAllowedFunctionalities = new Set([
      'purchased_plan',
      'upgrade_plan',
      'downgrade_plan',
      'trial_completion_reminder',
      'renewal_completion_reminder',
      'cancellation_reminder',
      'user_verification',
      'forgot_password',
      'resend_otp',
      'super_admin_reactivation'
    ]);

    // Fast check if current mail_type is always allowed
    const mail_type = templateData.mail_type || 'default_notification';
    if (alwaysAllowedFunctionalities.has(mail_type)) {
      return {
        ...templateData,
        allowed: { email: true, push: true, banner: true }
      };
    }

    // Check cache first
    const cacheKey = `${organization_id}:${mail_type}`;
    const cachedPreferences = notificationPreferencesCache.get(cacheKey);
    const now = Date.now();

    if (cachedPreferences && (now - cachedPreferences.timestamp) < CACHE_DURATION) {
      return {
        ...templateData,
        allowed: {
          email: cachedPreferences.email,
          push: cachedPreferences.push,
          banner: cachedPreferences.banner
        }
      };
    }

    // Get organization preferences for email, push, and banner in single query
    const preferences = await sequelize.query(
      `SELECT key, value, type FROM mo_notification_preferences WHERE organization_id = '${organization_id}' AND key IN ('mail', 'push', 'banner')`,
      {
        type: QueryTypes.SELECT,
        raw: true,
      },
    );

    // Create allowed object with default values (optimized)
    const allowed = {
      email: true,  // Default to true if no preference found
      push: true,   // Default to true if no preference found
      banner: true  // Default to true if no preference found
    };

    if (preferences.length) {
      // Update allowed flags based on preferences (optimized loop)
      for (const pref of preferences) {
        if (pref.key === 'mail') {
          allowed.email = pref.value;
        } else if (pref.key === 'push') {
          allowed.push = pref.value;
        } else if (pref.key === 'banner') {
          allowed.banner = pref.value;
        }
      }
    }

    // Cache the result
    notificationPreferencesCache.set(cacheKey, {
      email: allowed.email,
      push: allowed.push,
      banner: allowed.banner,
      timestamp: now
    });

    return {
      ...templateData,
      allowed: allowed
    };

  } catch (error) {
    console.error('Error checking notification preferences:', error);
    // Default to allowing all notifications if there's an error
    return {
      ...templateData,
      allowed: { email: true, push: true, banner: true }
    };
  }
};

/** Helper function to extract organization_id from ORGANIZATION_LOGO URL */
const extractOrgIdFromLogo = (logoUrl: string): string | null => {
  try {
    if (!logoUrl || typeof logoUrl !== 'string') return null;

    // Extract organization_id from logo URL pattern
    // Example: https://api.example.com/uploads/org_123/logo.png
    const orgIdMatch = logoUrl.match(/\/([^/]+)\/[^/]*logo/i);
    return orgIdMatch ? orgIdMatch[1] : null;
  } catch (error) {
    console.error('Error extracting organization_id from logo:', error);
    return null;
  }
};

// Cache for email to organization_id mapping
const emailOrgCache = new Map<string, { organization_id: string | null, timestamp: number }>();

/** Helper function to get organization_id from email - OPTIMIZED */
const getOrganizationIdFromEmail = async (email: string) => {
  try {
    if (!email) return null;

    // Check cache first
    const cachedResult = emailOrgCache.get(email);
    const now = Date.now();

    if (cachedResult && (now - cachedResult.timestamp) < CACHE_DURATION) {
      return cachedResult.organization_id;
    }

    // Handle multiple emails (comma-separated)
    const firstEmail = email.split(',')[0].trim();

    const user = await User.findOne({
      where: { user_email: firstEmail },
      attributes: ['organization_id'],
      raw: true
    });

    const organization_id = user?.organization_id || null;

    // Cache the result
    emailOrgCache.set(email, {
      organization_id: organization_id,
      timestamp: now
    });

    return organization_id;
  } catch (error) {
    console.error('Error getting organization_id from email:', error);
    return null;
  }
};

/** Helper function to get organization_id from deviceId (for push notifications) */
const getOrganizationIdFromDeviceId = async (deviceId: any) => {
  try {
    if (!deviceId) return null;

    // Handle array of device IDs - take the first one
    const firstDeviceId = Array.isArray(deviceId) ? deviceId[0] : deviceId;
    if (!firstDeviceId) return null;

    // Check cache first
    const cacheKey = `device:${firstDeviceId}`;
    const cachedResult = emailOrgCache.get(cacheKey);
    const now = Date.now();

    if (cachedResult && (now - cachedResult.timestamp) < CACHE_DURATION) {
      return cachedResult.organization_id;
    }

    // Query user by device token (assuming device tokens are stored in User model)
    const user = await sequelize.query(`
      SELECT organization_id
      FROM nv_users
      WHERE appToken = '${firstDeviceId}' OR webAppToken = '${firstDeviceId}'
      LIMIT 1
    `, {
      type: QueryTypes.SELECT,
      raw: true,
    });

    const organization_id = user?.organization_id || null;

    // Cache the result
    emailOrgCache.set(cacheKey, {
      organization_id: organization_id,
      timestamp: now
    });

    return organization_id;
  } catch (error) {
    console.error('Error getting organization_id from deviceId:', error);
    return null;
  }
};


const getUserWeekDays = async (userId: number, day: any) => {
  return await sequelize.query(`(SELECT * FROM nv_user_week_day WHERE user_id = ${userId} AND ${day} !='working' AND user_weekday_status='active')`, {
    type: QueryTypes.SELECT,
    raw: true,
  })
}

const permittedForAdmin = async (user_id: number, roleArray?: any) => {
  try {
    const query = `
  SELECT ur.*, r.role_name FROM nv_user_roles ur
  INNER JOIN nv_roles r ON ur.role_id = r.id
  WHERE ur.user_id = :user_id
  AND r.role_name IN (:roleArray)
`;

    const roleData = await sequelize.query(query, {
      replacements: { user_id, roleArray },
      type: QueryTypes.SELECT,
    });

    if (roleData.length > 0) {
      const roleNames = roleData.map((role: any) => role.role_name);

      if (
        roleNames.includes(ROLE_CONSTANT.DIRECTOR) ||
        roleNames.includes(ROLE_CONSTANT.HR)
      ) {
        const userQuery = `
      SELECT id FROM nv_users 
      WHERE id = :user_id 
      AND user_status NOT IN (:statuses)
    `;

        const userStatus = await sequelize.query(userQuery, {
          replacements: {
            user_id,
            statuses: ["pending", "deleted", "cancelled"],
          },
          type: QueryTypes.SELECT,
        });

        if (userStatus.length > 0) {
          return true;
        }
      } else {
        return true;
      }
    } else {
      return false;
    }
  } catch (error) {
    console.log("error", error);
  }
};

/**
 * Check if user has admin permissions using MORole system (mo_roles table)
 * @param user_id - User ID to check permissions for
 * @param organization_id - Organization ID for filtering
 * @param roleArray - Array of role names to check against (optional)
 * @returns Promise<boolean> - true if user has admin permissions, false otherwise
 */
const permittedForAdminMO = async (
  user_id: number,
  organization_id: string,
  roleArray?: string[]
): Promise<boolean> => {
  try {
    if (!user_id || !organization_id) {
      console.log("permittedForAdminMO: Missing required parameters");
      return false;
    }

    // Default admin roles if none provided
    const defaultAdminRoles = [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.HR,
      ROLE_CONSTANT.AREA_MANAGER,
      ROLE_CONSTANT.BRANCH_MANAGER,
      ROLE_CONSTANT.HOTEL_MANAGER
    ];

    const rolesToCheck = roleArray || defaultAdminRoles;

    // Get user details and role information using raw SQL
    const userRoleQuery = `
      SELECT
        u.id,
        u.user_role_id,
        u.user_status,
        u.organization_id,
        r.id as role_id,
        r.role_name,
        r.role_status
      FROM nv_users u
      INNER JOIN mo_roles r ON u.user_role_id = r.id
      WHERE u.id = :user_id
        AND u.organization_id = :organization_id
        AND u.user_status NOT IN (:pending, :deleted, :cancelled)
        AND r.role_status = 'active'
        AND r.organization_id = :organization_id
      LIMIT 1
    `;

    const userRoleResult = await sequelize.query(userRoleQuery, {
      replacements: {
        user_id,
        organization_id,
        pending: user_status.PENDING,
        deleted: user_status.DELETED,
        cancelled: user_status.CANCELLED
      },
      type: QueryTypes.SELECT,
    });

    if (!userRoleResult || userRoleResult.length === 0) {
      console.log("permittedForAdminMO: User not found, inactive, or has no valid role");
      return false;
    }

    const userRole: any = userRoleResult[0];

    // Check if user's role is in the permitted admin roles
    const hasAdminRole = rolesToCheck.includes(userRole.role_name);

    if (!hasAdminRole) {
      console.log(`permittedForAdminMO: User role '${userRole.role_name}' is not in permitted admin roles`);
      return false;
    }

    // Additional validation for specific roles (Director, HR)
    if (userRole.role_name === ROLE_CONSTANT.DIRECTOR || userRole.role_name === ROLE_CONSTANT.HR) {
      // For Director and HR roles, ensure user status is valid
      const userStatusQuery = `
        SELECT id
        FROM nv_users
        WHERE id = :user_id
          AND organization_id = :organization_id
          AND user_status NOT IN (:pending, :deleted, :cancelled)
        LIMIT 1
      `;

      const userStatusResult = await sequelize.query(userStatusQuery, {
        replacements: {
          user_id,
          organization_id,
          pending: user_status.PENDING,
          deleted: user_status.DELETED,
          cancelled: user_status.CANCELLED
        },
        type: QueryTypes.SELECT,
      });

      if (!userStatusResult || userStatusResult.length === 0) {
        console.log("permittedForAdminMO: Director/HR user status validation failed");
        return false;
      }
    }

    console.log(`permittedForAdminMO: User ${user_id} has admin permission with role '${userRole.role_name}'`);
    return true;

  } catch (error) {
    console.log("permittedForAdminMO error:", error);
    return false;
  }
};

/**
 * Helper function to determine platform from request headers
 * @param req - Request object with headers
 * @returns Platform number (1=web, 2=app)
 */
const getPlatformFromRequest = (req: any): number => {
  const platformType = req?.headers?.["platform-type"];
  return platformType == "ios" || platformType == "android" ? 2 : 1; // Default to web (1) if not specified
};

/**
 * Validate user permissions using raw SQL queries for MORole, MOPermission, and MOModule tables
 * @param user - User object or user ID
 * @param organization_id - Organization ID for filtering
 * @param module_slug - Module slug (e.g., 'dashboard', 'branch', 'staff') to check permission for
 * @param permission_type - Permission type (VIEW=1, CREATE=2, EDIT=4, DELETE=8) from ROLE_PERMISSIONS constant
 * @param platform - Platform type (1=web, 2=app, 3=both) - optional, defaults to web
 * @returns Promise<boolean> - true if user has permission, false otherwise
 */
const validateModulePermission = async (
  user: any,
  organization_id: string,
  module_slug: string,
  permission_type: number,
  platform: number = 1 // Default to web platform
): Promise<boolean | undefined> => {
  try {
    // Extract user ID if user object is passed
    const user_id = typeof user === 'object' ? user.id : user;

    if (!user_id || !module_slug || permission_type === undefined) {
      console.log("validateModulePermission: Missing required parameters");
      return false;
    }

    if (!user.organization_id) {
      return true;
    }

    // Get user details with organization validation using raw SQL
    const userQuery = `
      SELECT id, user_role_id, user_active_role_id, web_user_active_role_id, organization_id
      FROM nv_users
      WHERE id = :user_id
        AND organization_id = :organization_id
        AND user_status NOT IN (:pending, :deleted, :cancelled)
      LIMIT 1
    `;

    const userResult = await sequelize.query(userQuery, {
      replacements: {
        user_id,
        organization_id,
        pending: user_status.PENDING,
        deleted: user_status.DELETED,
        cancelled: user_status.CANCELLED
      },
      type: QueryTypes.SELECT,
    });

    if (!userResult || userResult.length === 0) {
      console.log("validateModulePermission: User not found or not in organization");
      return false;
    }

    const userDetails: any = userResult[0];

    // Prioritize user_role_id (MORole), then fallback to old role system
    const active_role_id = userDetails.user_role_id ||
      (platform === 1 ? userDetails.web_user_active_role_id : userDetails.user_active_role_id);

    if (!active_role_id) {
      console.log("validateModulePermission: User has no active role");
      return false;
    }


    if (!userDetails.organization_id) {
      return true
    }

    // If using user_role_id (MORole system), proceed with MORole validation
    if (userDetails.user_role_id && platform == 1) {
      // Check if the role exists and is active in the organization using raw SQL
      const roleQuery = `
        SELECT id, role_name, role_status, platform, additional_permissions
        FROM mo_roles
        WHERE id = :role_id
          AND organization_id = :organization_id
          AND role_status = 'active'
        LIMIT 1
      `;

      const roleResult = await sequelize.query(roleQuery, {
        replacements: {
          role_id: active_role_id,
          organization_id
        },
        type: QueryTypes.SELECT,
      });

      if (!roleResult || roleResult.length === 0) {
        console.log("validateModulePermission: User MORole not found or inactive");
        return false;
      }

      const userRole: any = roleResult[0];

      // Check if the role has access to the requested platform
      const rolePlatform = userRole.platform;
      if (rolePlatform !== 3 && rolePlatform !== platform) {
        console.log(`validateModulePermission: Role platform ${rolePlatform} doesn't match requested platform ${platform}`);
        return false;
      }

      // Check if the module exists globally using raw SQL
      const moduleQuery = `
        SELECT id, module, module_name
        FROM mo_modules
        WHERE module = :module_slug
        LIMIT 1
      `;

      const moduleResult = await sequelize.query(moduleQuery, {
        replacements: {
          module_slug
        },
        type: QueryTypes.SELECT,
      });

      if (!moduleResult || moduleResult.length === 0) {
        console.log(`validateModulePermission: Module '${module_slug}' not found globally`);
        return false;
      }

      const moduleData: any = moduleResult[0];

      // Check if the module is assigned to the organization using raw SQL
      const orgModuleQuery = `
        SELECT id, module_id, organization_id, status
        FROM mo_org_modules
        WHERE module_id = :module_id
          AND organization_id = :organization_id
          AND status = 'active'
        LIMIT 1
      `;

      const orgModuleResult = await sequelize.query(orgModuleQuery, {
        replacements: {
          module_id: moduleData.id,
          organization_id
        },
        type: QueryTypes.SELECT,
      });

      if (!orgModuleResult || orgModuleResult.length === 0) {
        console.log(`validateModulePermission: Module '${module_slug}' not assigned to organization ${organization_id}`);
        return false;
      }

      // Get user's permission for the specific module using raw SQL
      const permissionQuery = `
        SELECT permission, status
        FROM mo_permissions
        WHERE role_id = :role_id
          AND module_id = :module_id
          AND organization_id = :organization_id
          AND status = 'active'
        LIMIT 1
      `;

      const permissionResult = await sequelize.query(permissionQuery, {
        replacements: {
          role_id: active_role_id,
          module_id: moduleData.id,
          organization_id
        },
        type: QueryTypes.SELECT,
      });

      if (!permissionResult || permissionResult.length === 0) {
        console.log("validateModulePermission: No permission record found for user role, module, and platform");
        return false;
      }

      const userPermission: any = permissionResult[0];

      // Check if user has the required permission using bitwise AND
      const hasPermission = (userPermission.permission & permission_type) > 0;

      if (!hasPermission) {
        console.log(`validateModulePermission: User lacks required permission. Required: ${permission_type}, User has: ${userPermission.permission}`);
        return false;
      }

      console.log(`validateModulePermission: Permission granted for user ${user_id}, module ${module_slug}, permission ${permission_type}`);
      return true;
    } else {
      const status = await permittedForAdmin(user_id, [ROLE_CONSTANT.SUPER_ADMIN, ROLE_CONSTANT.ADMIN, ROLE_CONSTANT.DIRECTOR, ROLE_CONSTANT.HR, ROLE_CONSTANT.AREA_MANAGER, ROLE_CONSTANT.BRANCH_MANAGER, ROLE_CONSTANT.HOTEL_MANAGER]);
      return status;
    }

  } catch (error) {
    console.log("validateModulePermission error:", error);
    return false;
  }
};



/**
 * Get MORole details by role IDs using raw SQL
 * @param roleIds - Array of role IDs
 * @returns Promise<any[]> - Array of role objects
 */
const getRolesMo = async (roleIds: number[]): Promise<any[]> => {
  try {
    if (!roleIds || roleIds.length === 0) {
      return [];
    }

    const roleQuery = `
      SELECT *
      FROM mo_roles
      WHERE id IN (${roleIds.map(() => '?').join(',')})
        AND role_status = 'active'
      ORDER BY role_name ASC
    `;

    const roles = await sequelize.query(roleQuery, {
      replacements: roleIds,
      type: QueryTypes.SELECT,
    });

    return roles || [];
  } catch (error) {
    console.log('Error getting MORole details:', error);
    return [];
  }
};

// Get user by email
const getUserByEmail = async (email: string) => {
  const user = await sequelize.query(
    `SELECT * FROM nv_users WHERE user_email = :email AND user_status NOT IN ('cancelled', 'deleted') LIMIT 1`,
    { replacements: { email }, type: QueryTypes.SELECT, raw: true }
  );
  return user && user.length > 0 ? user[0] : null;
};

// Get branch by name
const getBranchByName = async (name: string) => {
  const branch = await sequelize.query(
    `SELECT * FROM nv_branches WHERE branch_name = :name LIMIT 1`,
    { replacements: { name }, type: QueryTypes.SELECT, raw: true }
  );
  return branch && branch.length > 0 ? branch[0] : null;
};

// Helper to get best shifts for users
const getBestShifts = async (organization_id: string, filters: { userId?: string, roleId?: string, departmentId?: string, branchId?: string, startTime?: string, endTime?: string }) => {
  const { userId, roleId, departmentId, branchId, startTime, endTime } = filters;
  const whereClauses = ["userId IS NOT NULL", "status != 'deleted'"];
  const replacements: any = {};

  if (userId) {
    whereClauses.push("userId = :userId");
    replacements.userId = userId;
  }
  if (roleId) {
    whereClauses.push("roleId = :roleId");
    replacements.roleId = roleId;
  }
  if (departmentId) {
    whereClauses.push("departmentId = :departmentId");
    replacements.departmentId = departmentId;
  }
  if (branchId) {
    whereClauses.push("branchId = :branchId");
    replacements.branchId = branchId;
  }
  // Only filter by time (HH:mm) if startTime or endTime is provided
  if (startTime) {
    whereClauses.push("TIME(startTime) = TIME(:startTime)");
    replacements.startTime = startTime;
  }
  if (endTime) {
    whereClauses.push("TIME(endTime) = TIME(:endTime)");
    replacements.endTime = endTime;
  }

  // Always filter by organization
  whereClauses.push("organization_id = :organization_id");
  replacements.organization_id = organization_id;

  const whereSQL = whereClauses.length ? `WHERE ${whereClauses.join(' AND ')}` : '';

  const query = `
    SELECT
      userId,
      CONCAT(TIME_FORMAT(TIME(startTime), '%H:%i'), '-', TIME_FORMAT(TIME(endTime), '%H:%i')) AS time_slot,
      COUNT(*) AS shift_count
    FROM
      shifts
    ${whereSQL}
    GROUP BY
      userId, time_slot
    ORDER BY
      shift_count DESC, userId
  `;

  return sequelize.query(query, {
    replacements,
    type: QueryTypes.SELECT,
    raw: true
  });
};

const getGeneralSettingObj = async (organization_id: any = null) => {
  try {
    // Get all settings for the organization
    const settingsQuery = `
      SELECT s.key, s.value, i.item_location
      FROM nv_settings s
      LEFT JOIN nv_items i ON s.value = i.id AND s.key IN ('brand_logo', 'employer_sign')
      WHERE s.setting_status = 'active' 
        AND s.organization_id = ?`;

    const settings = await sequelize.query(settingsQuery, {
      replacements: [organization_id],
      type: QueryTypes.SELECT,
      raw: true,
    });

    if (settings.length > 0) {
      const settingObj: any = {};

      for (const setting of settings) {
        const key = setting.key;
        const value = setting.value;

        settingObj[key] = value;

        // Handle brand_logo
        if (key === "brand_logo" && setting.item_location) {
          settingObj["brand_logo_link"] = `${global.config.API_UPLOAD_URL}${setting.item_location}`;
        }
      }

      return settingObj;
    } else {
      return {};
    }
  } catch (error) {
    console.log("error", error);
    return {};
  }
};

export {
  getUserIdFromUserTable,
  getPaginatedItems,
  getPagination,
  getDurationBasedOnDays,
  readHTMLFile,
  checkUserRole,
  getRoles,
  getRolesMo,
  getUser,
  getUserAllRoles,
  getUserSession,
  createNotification,
  getBranchDetails,
  getUsers,
  permittedForAdmin,
  permittedForAdminMO,
  getDepartmentDetails,
  getUserWeekDays,
  getUserDeviceId,
  validateModulePermission,
  getPlatformFromRequest,
  getUserByEmail,
  getBranchByName,
  getBestShifts,
  getGeneralSettingObj
};
