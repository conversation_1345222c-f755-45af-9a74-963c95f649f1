import express from "express";
import {
  getShiftSettings,
  updateShiftSettings,
  deleteShiftSetting,
  resetShiftSettings,
} from "../../controller/shiftSettings.controller";
import {
  getShiftSettingsValidator,
  updateShiftSettingsValidator,
  deleteShiftSettingValidator,
  resetShiftSettingsValidator,
} from "../../validator/shiftSettings.validator";

const router = express.Router();

/**
 * @swagger
 * /api/private/shift-settings:
 *   get:
 *     summary: Get shift settings
 *     description: Retrieve shift settings for a specific branch or global settings
 *     tags: [Shift Settings]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         description: Branch ID (optional, if not provided returns global settings)
 *     responses:
 *       200:
 *         description: Shift settings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     allow_weekly_hours_exceed:
 *                       type: boolean
 *                     allow_non_preferred_days:
 *                       type: boolean
 *                     allow_unavailable_users:
 *                       type: boolean
 *                     allow_already_assigned:
 *                       type: boolean
 *                     allow_shift_type_mismatch:
 *                       type: boolean
 *                     allow_leave_users:
 *                       type: boolean
 *                     allow_no_availability_record:
 *                       type: boolean
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
router.get("/", getShiftSettingsValidator, getShiftSettings);

/**
 * @swagger
 * /api/private/shift-settings:
 *   put:
 *     summary: Update shift settings
 *     description: Create or update shift settings for a specific branch or globally
 *     tags: [Shift Settings]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               branch_id:
 *                 type: integer
 *                 description: Branch ID (optional, if not provided updates global settings)
 *               settings:
 *                 type: object
 *                 required: true
 *                 properties:
 *                   allow_weekly_hours_exceed:
 *                     type: boolean
 *                   allow_non_preferred_days:
 *                     type: boolean
 *                   allow_unavailable_users:
 *                     type: boolean
 *                   allow_already_assigned:
 *                     type: boolean
 *                   allow_shift_type_mismatch:
 *                     type: boolean
 *                   allow_leave_users:
 *                     type: boolean
 *                   allow_no_availability_record:
 *                     type: boolean
 *     responses:
 *       200:
 *         description: Shift settings updated successfully
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
router.put("/", updateShiftSettingsValidator, updateShiftSettings);

/**
 * @swagger
 * /api/private/shift-settings/{key}:
 *   delete:
 *     summary: Delete a specific shift setting
 *     description: Delete a specific shift setting by key
 *     tags: [Shift Settings]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: key
 *         required: true
 *         schema:
 *           type: string
 *           enum: [allow_weekly_hours_exceed, allow_non_preferred_days, allow_unavailable_users, allow_already_assigned, allow_shift_type_mismatch, allow_leave_users, allow_no_availability_record]
 *         description: Setting key to delete
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         description: Branch ID (optional, if not provided deletes global setting)
 *     responses:
 *       200:
 *         description: Shift setting deleted successfully
 *       400:
 *         description: Bad request
 *       404:
 *         description: Setting not found
 *       500:
 *         description: Internal server error
 */
router.delete("/:key", deleteShiftSettingValidator, deleteShiftSetting);

/**
 * @swagger
 * /api/private/shift-settings/reset:
 *   post:
 *     summary: Reset shift settings to default values
 *     description: Reset all shift settings to their default values
 *     tags: [Shift Settings]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         description: Branch ID (optional, if not provided resets global settings)
 *     responses:
 *       200:
 *         description: Shift settings reset successfully
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
router.post("/reset", resetShiftSettingsValidator, resetShiftSettings);

export default router;
