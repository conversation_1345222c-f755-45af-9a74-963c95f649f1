import { Request, Response } from "express";
import { ShiftSettings, VALID_SHIFT_SETTING_KEYS, DEFAULT_SHIFT_SETTINGS } from "../models/shiftSettings";
import { Op } from "sequelize";

/**
 * Get shift settings for a specific branch or global settings
 * @param req - Express request object
 * @param res - Express response object
 * @returns Response with shift settings as key-value pairs
 */
const getShiftSettings = async (req: Request, res: Response): Promise<any> => {
  try {
    const { branch_id } = req.query;
    const organization_id = req.user.organization_id;

    // Build query conditions
    const whereConditions: any = {
      organization_id,
    };

    if (branch_id) {
      // If branch_id is provided, get settings for that branch and global settings (null branch_id)
      whereConditions.branch_id = {
        [Op.or]: [Number(branch_id), null],
      };
    } else {
      // If no branch_id, only get global settings
      whereConditions.branch_id = null;
    }

    // Fetch settings from database
    const settings = await ShiftSettings.findAll({
      where: whereConditions,
      attributes: ["key", "value", "branch_id"],
      raw: true,
    });

    // Create result object with default values
    const result = { ...DEFAULT_SHIFT_SETTINGS };

    // Process settings - branch-specific settings override global settings
    const globalSettings: any = {};
    const branchSettings: any = {};

    settings.forEach((setting: any) => {
      // Parse the value (convert string to boolean, number, etc.)
      let parsedValue = setting.value;
      try {
        parsedValue = JSON.parse(setting.value);
      } catch (e) {
        // If parsing fails, keep the original string value
      }

      if (setting.branch_id === null) {
        globalSettings[setting.key] = parsedValue;
      } else {
        branchSettings[setting.key] = parsedValue;
      }
    });

    // Merge settings (branch settings override global settings)
    Object.assign(result, globalSettings, branchSettings);

    return res.status(200).send({
      status: true,
      message: res.__("SHIFT_SETTINGS_FETCHED_SUCCESSFULLY"),
      data: result,
    });
  } catch (error) {
    console.log("Error fetching shift settings:", error);
    return res.status(500).send({
      status: false,
      message: res.__("ERROR_FETCHING_SHIFT_SETTINGS"),
      error,
    });
  }
};

/**
 * Create or update shift settings
 * @param req - Express request object
 * @param res - Express response object
 * @returns Response with updated settings
 */
const updateShiftSettings = async (req: Request, res: Response): Promise<any> => {
  try {
    const { branch_id, settings } = req.body;
    const organization_id = req.user.organization_id;
    const user_id = req.user.id;

    // Validate settings keys
    const invalidKeys = Object.keys(settings).filter(
      (key) => !VALID_SHIFT_SETTING_KEYS.includes(key)
    );

    if (invalidKeys.length > 0) {
      return res.status(400).send({
        status: false,
        message: res.__("INVALID_SETTING_KEYS"),
        invalidKeys,
      });
    }

    // Process each setting
    const updatedSettings = [];
    for (const [key, value] of Object.entries(settings)) {
      // Convert value to string for storage
      const stringValue = typeof value === "object" ? JSON.stringify(value) : String(value);

      // Find existing setting
      const existingSettings = await ShiftSettings.findOne({
        where: {
          organization_id,
          branch_id: branch_id || null,
          key,
        },
      });

      if (existingSettings) {
        // Update existing setting
        await existingSettings.update({
          value: stringValue,
          updatedBy: user_id,
        });
        updatedSettings.push(existingSettings);
      } else {
        // Create new setting
        const newSetting = await ShiftSettings.create({
          organization_id,
          branch_id: branch_id || null,
          key,
          value: stringValue,
          createdBy: user_id,
          updatedBy: user_id,
        });
        updatedSettings.push(newSetting);
      }
    }

    return res.status(200).send({
      status: true,
      message: res.__("SHIFT_SETTINGS_UPDATED_SUCCESSFULLY"),
      data: updatedSettings,
    });
  } catch (error) {
    console.log("Error updating shift settings:", error);
    return res.status(500).send({
      status: false,
      message: res.__("ERROR_UPDATING_SHIFT_SETTINGS"),
      error,
    });
  }
};

/**
 * Delete a specific shift setting
 * @param req - Express request object
 * @param res - Express response object
 * @returns Response with deletion status
 */
const deleteShiftSetting = async (req: Request, res: Response): Promise<any> => {
  try {
    const { key } = req.params;
    const { branch_id } = req.query;
    const organization_id = req.user.organization_id;

    // Validate key
    if (!VALID_SHIFT_SETTING_KEYS.includes(key)) {
      return res.status(400).send({
        status: false,
        message: res.__("INVALID_SETTING_KEY"),
      });
    }

    // Build query conditions
    const whereConditions: any = {
      organization_id,
      key,
    };

    if (branch_id) {
      whereConditions.branch_id = Number(branch_id);
    } else {
      whereConditions.branch_id = null;
    }

    // Find and delete the setting
    const setting = await ShiftSettings.findOne({
      where: whereConditions,
    });

    if (!setting) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_SETTING_NOT_FOUND"),
      });
    }

    await setting.destroy();

    return res.status(200).send({
      status: true,
      message: res.__("SHIFT_SETTING_DELETED_SUCCESSFULLY"),
    });
  } catch (error) {
    console.log("Error deleting shift setting:", error);
    return res.status(500).send({
      status: false,
      message: res.__("ERROR_DELETING_SHIFT_SETTING"),
      error,
    });
  }
};

/**
 * Reset shift settings to default values
 * @param req - Express request object
 * @param res - Express response object
 * @returns Response with reset status
 */
const resetShiftSettings = async (req: Request, res: Response): Promise<any> => {
  try {
    const { branch_id } = req.query;
    const organization_id = req.user.organization_id;
    const user_id = req.user.id;

    // Build query conditions
    const whereConditions: any = {
      organization_id,
    };

    if (branch_id) {
      whereConditions.branch_id = Number(branch_id);
    } else {
      whereConditions.branch_id = null;
    }

    // Delete existing settings
    await ShiftSettings.destroy({
      where: whereConditions,
    });

    // Create default settings
    const defaultSettings = [];
    for (const [key, value] of Object.entries(DEFAULT_SHIFT_SETTINGS)) {
      const stringValue = typeof value === "object" ? JSON.stringify(value) : String(value);
      
      const newSetting = await ShiftSettings.create({
        organization_id,
        branch_id: branch_id ? Number(branch_id) : null,
        key,
        value: stringValue,
        createdBy: user_id,
        updatedBy: user_id,
      });
      
      defaultSettings.push(newSetting);
    }

    return res.status(200).send({
      status: true,
      message: res.__("SHIFT_SETTINGS_RESET_SUCCESSFULLY"),
      data: DEFAULT_SHIFT_SETTINGS,
    });
  } catch (error) {
    console.log("Error resetting shift settings:", error);
    return res.status(500).send({
      status: false,
      message: res.__("ERROR_RESETTING_SHIFT_SETTINGS"),
      error,
    });
  }
};

/**
 * Helper function to get shift settings for internal use
 * @param organization_id - Organization ID
 * @param branch_id - Branch ID (optional)
 * @returns Promise<object> - Shift settings as key-value pairs
 */
const getShiftSettingsHelper = async (
  organization_id: string,
  branch_id?: number | null
): Promise<any> => {
  try {
    // Build query conditions
    const whereConditions: any = {
      organization_id,
    };

    if (branch_id) {
      // If branch_id is provided, get settings for that branch and global settings (null branch_id)
      whereConditions.branch_id = {
        [Op.or]: [branch_id, null],
      };
    } else {
      // If no branch_id, only get global settings
      whereConditions.branch_id = null;
    }

    // Fetch settings from database
    const settings = await ShiftSettings.findAll({
      where: whereConditions,
      attributes: ["key", "value", "branch_id"],
      raw: true,
    });

    // Create result object with default values
    const result = { ...DEFAULT_SHIFT_SETTINGS };

    // Process settings - branch-specific settings override global settings
    const globalSettings: any = {};
    const branchSettings: any = {};

    settings.forEach((setting: any) => {
      // Parse the value (convert string to boolean, number, etc.)
      let parsedValue = setting.value;
      try {
        parsedValue = JSON.parse(setting.value);
      } catch {
        // If parsing fails, keep the original string value
      }

      if (setting.branch_id === null) {
        globalSettings[setting.key] = parsedValue;
      } else {
        branchSettings[setting.key] = parsedValue;
      }
    });

    // Merge settings (branch settings override global settings)
    Object.assign(result, globalSettings, branchSettings);

    return result;
  } catch (error) {
    console.log("Error fetching shift settings:", error);
    return DEFAULT_SHIFT_SETTINGS;
  }
};

export {
  getShiftSettings,
  updateShiftSettings,
  deleteShiftSetting,
  resetShiftSettings,
  getShiftSettingsHelper,
};
