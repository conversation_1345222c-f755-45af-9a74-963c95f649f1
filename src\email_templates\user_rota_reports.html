<!DOCTYPE html>
<html>

<head>
    <title>Rota Reports</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #ffffff;
            color: #333333;
        }

        .report-container {
            max-width: 100%;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            border-bottom: 2px solid #0070C0;
            padding-bottom: 10px;
        }

        .report-title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #0070C0;
            margin-bottom: 10px;
            flex-grow: 1;
        }

        .report-details {
            font-size: 12px;
            color: #666666;
        }

        .report-details p {
            margin: 2px 0;
        }

        .table-container {
            background-color: #ffffff;
            border: 1px solid #dddddd;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 15px;
        }

        .dsr-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
        }

        th,
        td {
            border: 1px solid #dddddd;
            text-align: center;
            padding: 6px 4px;
            vertical-align: middle;
        }

        th {
            background-color: #0070C0;
            color: #ffffff;
            font-weight: bold;
            font-size: 11px;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tr:hover {
            background-color: #f0f0f0;
        }

        .total-row {
            font-weight: bold;
            background-color: #e6ffe6;
        }

        .logo-placeholder {
            width: 100px;
            height: 50px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: #666;
        }

        @media print {
            body {
                background-color: #ffffff;
                margin: 0;
                padding: 10px;
            }
            
            .table-container {
                box-shadow: none;
                border: 1px solid #000;
            }
            
            th {
                background-color: #0070C0 !important;
                color: #ffffff !important;
            }
        }
    </style>
</head>

<body>
    <div class="report-container">
        <div class="header">
            <div class="report-title">Rota Reports</div>
            <div class="logo-placeholder">
                {{#if NAMASTE_LOGO}}
                    <img src="{{NAMASTE_LOGO}}" style="max-width: 100px; max-height: 50px;">
                {{else}}
                    LOGO
                {{/if}}
            </div>
        </div>
        
        <div class="report-details">
            <p><strong>Date:</strong> {{current_date}}</p>
            <p><strong>Period:</strong> {{period_from}} to {{period_to}}</p>
            {{#if branch_filter}}
            <p><strong>Branch:</strong> {{branch_filter}}</p>
            {{/if}}
            {{#if department_filter}}
            <p><strong>Department:</strong> {{department_filter}}</p>
            {{/if}}
            {{#if GENERATED_BY_USER}}
            <p><strong>Generated By:</strong> {{GENERATED_BY_USER}}</p>
            {{/if}}
        </div>
        
        <div class="table-container">
            <table class="dsr-table">
                <thead>
                    <tr>
                        <th>Employment Number</th>
                        <th>Full Name</th>
                        <th>Branch</th>
                        <th>Department</th>
                        <th>Total Shifts</th>
                        <th>Total Hours</th>
                        <th>Total Break Hours</th>
                    </tr>
                </thead>
                <tbody>
                    {{#each rota_data}}
                    <tr>
                        <td>{{employment_number}}</td>
                        <td>{{user_full_name}}</td>
                        <td>{{branch}}</td>
                        <td>{{department}}</td>
                        <td>{{total_shifts}}</td>
                        <td>{{total_hours}}</td>
                        <td>{{total_break_hours}}</td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
