import { Op, QueryTypes } from "sequelize";
import { Request, Response } from "express";
import { Shift, ShiftStatus } from "../models/shifts";
import moment from "moment";
import {
  createNotification,
  getBranchDetails,
  getDepartmentDetails,
  getPlatformFromRequest,
  getRoles,
  getRolesMo,
  getUser,
  getUserDeviceId,
  getUsers,
  permittedForAdmin,
  // permittedForAdmin,
  permittedForAdminMO,
  validateModulePermission,
  getUserByEmail,
  getBranchByName,
  getBestShifts,
  readHTMLFile,
  getGeneralSettingObj
} from "../helper/common";
import { Swap } from "../models/swap";
import { DayOff } from "../models/dayOff";
import i18n from "../helper/i18n";
import {
  NOTIFICATIONS,
  ROLE_CONSTANT,
  ROLE_PERMISSIONS,
} from "../helper/constant";
import ShiftHistory from "../models/shiftHistory";
import { Drop } from "../models/drop";
import { Availability, AvailabilityStatus } from "../models/availability";
import ExcelJS from 'exceljs';
import { sequelize } from "../models";
import fs from "fs";
import path from "path";
import handlebars from "handlebars";
import { generateFile } from "../helper/fileGenerator";

// Add a type guard for req.file
interface MulterRequest extends Request {
  file?: Express.Multer.File;
}

const commonShiftValidator = async (req: any) => {
  try {
    const { userId, startTime, endTime, isOpen, swapId, id, status } = req.body;

    const findShift: any = swapId
      ? await Shift.findOne({
        where: {
          id: swapId,
          organization_id: req.user.organization_id
        }
      })
      : {};

    const checkPermission = await permittedForAdminMO(req.user?.id, req.user.organization_id, [
      ROLE_CONSTANT.SUPER_ADMIN
    ]);

    if (!checkPermission) {
      if (startTime >= endTime) {
        return {
          data: null,
          message: i18n.__("START_TIME_MUST_BE_BEFORE_END_TIME"),
        };
      }

      if (startTime < new Date()) {
        return {
          data: null,
          message: i18n.__("START_TIME_MUST_BE_AFTER_TODAY"),
        };
      }

      if (endTime < new Date()) {
        return {
          data: null,
          message: i18n.__("END_TIME_MUST_BE_AFTER_TODAY"),
        };
      }
    }

    const existingWhere: any = {
      [Op.or]: [
        {
          startTime: {
            [Op.and]: {
              [Op.gte]:
                findShift && findShift.startTime
                  ? new Date(findShift.startTime)
                  : new Date(startTime),
              [Op.lt]:
                findShift && findShift.endTime
                  ? new Date(findShift.endTime)
                  : new Date(endTime),
            },
          },
        },
        {
          endTime: {
            [Op.and]: {
              [Op.gt]:
                findShift && findShift.startTime
                  ? new Date(findShift.startTime)
                  : new Date(startTime),
              [Op.lte]:
                findShift && findShift.endTime
                  ? new Date(findShift.endTime)
                  : new Date(endTime),
            },
          },
        },
        {
          startTime: {
            [Op.lte]:
              findShift && findShift.startTime
                ? new Date(findShift.startTime)
                : new Date(startTime),
          },
          endTime: {
            [Op.gt]:
              findShift && findShift.endTime
                ? new Date(findShift.endTime)
                : new Date(endTime),
          },
        },
      ],
      isOpen: isOpen == true ? true : false,
      isDropped: false,
      organization_id: req.user.organization_id,
    };

    if (id && id != null) {
      existingWhere.id = {
        [Op.ne]: id,
      };
    } else {
      existingWhere.status = status ? status : "active";
    }

    if (!existingWhere.isOpen && userId) {
      existingWhere.userId = userId;
    }
    const existingShift = await Shift.findOne({
      where: existingWhere,
    });

    if (userId) {
      // const checkDayOff = await DayOff.findOne({
      //   where: {
      //     userId,
      //     status: DayOffStatus.active,
      //     date: moment(startTime).format("YYYY-MM-DD"),
      //   },
      // });
      // const checkUserWeekDay = await getUserWeekDays(userId, `${moment(startTime).format('dddd')}`.toLowerCase())

      // if ((checkDayOff && checkDayOff.dayOff) || checkUserWeekDay.length > 0) {
      //   return {
      //     data: null,
      //     message: `User is on day off`,
      //   };
      // }
      const findUser = await getUser(userId);
      if (!findUser) {
        return {
          data: null,
          message: i18n.__("ERROR_USER_NOT_FOUND"),
        };
      }
    }

    if (existingShift) {
      return {
        data: existingShift,
        message: `Shift time clash detected ${moment(startTime).format("HH:mm A")} to ${moment(endTime).format("HH:mm A")}`,
      };
    }

    return null;
  } catch (error: any) {
    console.log(error);
    return {
      data: null,
      message: error?.message,
    };
  }
};

const addShift = async (req: Request, res: Response): Promise<any> => {
  try {
    const body = req.body;

    const checkPermission = await validateModulePermission(req.user, req.user.organization_id, "rotas", ROLE_PERMISSIONS.CREATE, getPlatformFromRequest(req));
    if (!checkPermission)
      return res
        .status(403)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });

    const {
      userId,
      startTime,
      endTime,
      status,
      minutesBreak,
      branchId,
      departmentId,
      role,
      isOpen,
      isPublished,
      isSwap,
      acknowledged,
      notes,
    } = body;

    const validateShift = await commonShiftValidator(req);
    if (validateShift) {
      return res.status(409).send({
        status: false,
        message: validateShift?.message,
      });
    }
    const findUser = await getUser(userId);

    const newShift = await Shift.create({
      userId,
      startTime: new Date(startTime),
      endTime: new Date(endTime),
      status,
      minutesBreak,
      branchId,
      departmentId,
      roleId: role,
      isOpen: isOpen ? true : false,
      isPublished: isPublished ? true : false,
      isSwap: isSwap ? true : false,
      acknowledged: acknowledged ? true : false,
      isDropped: false,
      createdBy: req?.user?.id,
      updatedBy: req?.user?.id,
      notes: notes,
      organization_id: req.user.organization_id,
    });

    const changes = ShiftHistory.generateChanges({}, newShift);
    await ShiftHistory.logChange(newShift.id, req.user?.id, "CREATE", changes, {
      ip: req?.headers?.["ip-address"] ? `${req?.headers?.["ip-address"]}` : "",
      userAgent: `${req?.headers?.["user-agent"]}`,
    });

    const notificationObj: any = {
      deviceId: await getUserDeviceId([findUser]),
      content: NOTIFICATIONS.SHIFT_ADD.CONTENT(
        findUser?.user_first_name,
        moment(startTime).format("YYYY-MM-DD HH:mm A"),
        moment(endTime).format("YYYY-MM-DD HH:mm A"),
      ),
      data: {
        notification_content: NOTIFICATIONS.SHIFT_ADD.CONTENT(
          findUser?.user_first_name,
          moment(startTime).format("YYYY-MM-DD HH:mm A"),
          moment(endTime).format("YYYY-MM-DD HH:mm A"),
        ),
        notification_subject: NOTIFICATIONS.SHIFT_ADD.HEADER,
        notification_image: null,
        to_user_id: userId,
        redirection_type: "shift_details",
        notification_type: "shifts_add",
        notification_status: "sent",
        reference_id: newShift.id,
        redirection_object: { id: newShift.id },
        from_user_id: req.user.id,
        created_by: req.user.id,
        updated_by: req.user.id
      },
      type: "shift",
      subject: NOTIFICATIONS.SHIFT_ADD.HEADER,
    };
    if (isPublished && acknowledged && !isOpen && notificationObj?.deviceId?.length > 0) {
      await createNotification(notificationObj?.data, req, notificationObj?.deviceId);
    }

    return res.status(201).send({
      status: true,
      message: res.__("SHIFT_ADDED"),
      // data: newShift,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

/**
 * Create multiple shifts in batch
 * @param req - Express request object
 * @param res - Express response object
 * @returns Response with batch creation results
 */
const addShiftsBatch = async (req: Request, res: Response): Promise<any> => {
  try {
    const { shifts } = req.body;

    // Validate input
    if (!shifts || !Array.isArray(shifts) || shifts.length === 0) {
      return res.status(400).send({
        status: false,
        message: res.__("SHIFTS_ARRAY_REQUIRED"),
      });
    }

    // Check permission
    const checkPermission = await validateModulePermission(
      req.user,
      req.user.organization_id,
      "rotas",
      ROLE_PERMISSIONS.CREATE,
      getPlatformFromRequest(req)
    );

    if (!checkPermission) {
      return res.status(403).json({
        status: false,
        message: res.__("PERMISSION_DENIED")
      });
    }

    const results = {
      successful: [] as any[],
      failed: [] as any[],
      totalProcessed: 0
    };

    // Process each shift
    for (let i = 0; i < shifts.length; i++) {
      const shiftData = shifts[i];
      results.totalProcessed++;

      try {
        // Validate individual shift
        const tempReq = {
          ...req,
          body: shiftData
        };

        const validateShift = await commonShiftValidator(tempReq);
        if (validateShift) {
          results.failed.push({
            index: i,
            shift: shiftData,
            error: validateShift.message
          });
          continue;
        }

        // Create the shift
        const newShift = await Shift.create({
          userId: shiftData.userId,
          startTime: new Date(shiftData.startTime),
          endTime: new Date(shiftData.endTime),
          status: shiftData.status || ShiftStatus.active,
          minutesBreak: shiftData.minutesBreak || 0,
          branchId: shiftData.branchId,
          departmentId: shiftData.departmentId,
          roleId: shiftData.role || shiftData.roleId,
          isOpen: shiftData.isOpen ? true : false,
          isPublished: shiftData.isPublished ? true : false,
          isSwap: shiftData.isSwap ? true : false,
          acknowledged: shiftData.acknowledged ? true : false,
          isDropped: false,
          createdBy: req.user.id,
          updatedBy: req.user.id,
          notes: shiftData.notes || "",
          organization_id: req.user.organization_id,
        });

        // Log shift history
        const changes = ShiftHistory.generateChanges({}, newShift);
        await ShiftHistory.logChange(newShift.id, req.user.id, "CREATE", changes, {
          ip: req?.headers?.["ip-address"] ? `${req?.headers?.["ip-address"]}` : "",
          userAgent: `${req?.headers?.["user-agent"]}`,
        });

        results.successful.push({
          index: i,
          shiftId: newShift.id,
          shift: newShift
        });

        // Send notification if required
        if (shiftData.isPublished && shiftData.acknowledged && !shiftData.isOpen && shiftData.userId) {
          const findUser = await getUser(shiftData.userId);
          const notificationObj = await getUserDeviceId(shiftData.userId);

          if (notificationObj?.deviceId?.length > 0) {
            const notificationData = {
              data: {
                notification_content: NOTIFICATIONS.SHIFT_ADD.CONTENT(
                  findUser?.user_first_name,
                  moment(shiftData.startTime).format("YYYY-MM-DD HH:mm A"),
                  moment(shiftData.endTime).format("YYYY-MM-DD HH:mm A"),
                ),
                notification_subject: NOTIFICATIONS.SHIFT_ADD.HEADER,
                notification_image: null,
                to_user_id: shiftData.userId,
                redirection_type: "shift_details",
                notification_type: "shifts_add",
                notification_status: "sent",
                reference_id: newShift.id,
                redirection_object: { id: newShift.id },
                from_user_id: req.user.id,
                created_by: req.user.id,
                updated_by: req.user.id
              },
              type: "shift",
              subject: NOTIFICATIONS.SHIFT_ADD.HEADER,
            };

            await createNotification(notificationData.data, req, notificationObj.deviceId);
          }
        }

      } catch (shiftError: any) {
        console.log(`Error creating shift at index ${i}:`, shiftError);
        results.failed.push({
          index: i,
          shift: shiftData,
          error: shiftError?.message || "Unknown error occurred"
        });
      }
    }

    // Prepare response message
    let message = res.__("BATCH_SHIFTS_PROCESSED");
    if (results.failed.length === 0) {
      message = res.__("ALL_SHIFTS_CREATED_SUCCESSFULLY");
    } else if (results.successful.length === 0) {
      message = res.__("ALL_SHIFTS_FAILED_TO_CREATE");
    }

    const statusCode = results.successful.length > 0 ? 201 : 400;

    return res.status(statusCode).send({
      status: results.successful.length > 0,
      message: message,
      data: {
        summary: {
          totalProcessed: results.totalProcessed,
          successful: results.successful.length,
          failed: results.failed.length
        },
        results: results
      }
    });

  } catch (error) {
    console.log("Batch shift creation error:", error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

const getShifts = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      userId,
      from,
      to,
      role,
      branchId,
      departmentId,
      isAdmin,
      isOpen,
      isDashboard,
      sortBy,
      sortOrder,
      status,
      shiftId,
      includeAvailability,
      page,
      size
    }: any = req.query;

    let where: any = {
      status: status ? status : { [Op.not]: ShiftStatus.deleted },
      organization_id: req.user.organization_id
    };

    if (isDashboard && isDashboard === "true") {
      where = {
        ...where,
        isPublished: true
      }
    }

    const checkPermission = await validateModulePermission(req.user, req.user.organization_id, "rotas", ROLE_PERMISSIONS.VIEW, getPlatformFromRequest(req));

    if (!checkPermission) {
      return res.status(403).json({
        status: false,
        message: res.__("PERMISSION_DENIED")
      });
    }

    if (userId && role) {
      where = {
        ...where,
        [Op.or]: [
          {
            userId: userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId,
            roleId: role?.toString()?.split(",")?.length > 1 ? { [Op.in]: role?.toString()?.split(",")?.map(Number) } : role,
          },
          {
            userId: userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId,
            roleId: { [Op.not]: role },
          },
          {
            roleId: role?.toString()?.split(",")?.length > 1 ? { [Op.in]: role?.toString()?.split(",")?.map(Number) } : role,
            isOpen: true,
          }
        ]
      }
    } else if (!userId && role) {
      where = {
        ...where,
        roleId: role?.toString()?.split(",")?.length > 1 ? { [Op.in]: role?.toString()?.split(",")?.map(Number) } : role
      }
    } else if (userId && !role) {
      if (isAdmin === "true") {
        where = {
          ...where,
          userId: userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId
        }
      } else {
        where = {
          ...where,
          userId: req?.user?.id
        }
      }
    }

    // Preserve admin and user filtering logic
    if (isAdmin === "true") {
      if (userId && !role) where.userId = userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId;
    }
    //  else {
    //   // if (!role && isOpen !== "true" && !userId) where.userId = req?.user?.id;

    // }
    if (isAdmin != "true") {
      where.isPublished = 1;
    }

    // Preserve `isOpen` condition
    if (isOpen === "true") {
      where.isOpen = true;
      where.userId = { [Op.is]: userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId ? userId : null };
    }

    // Preserve role and location filtering
    // if (role) where.roleId = role;
    if (branchId) where.branchId = branchId?.toString()?.split(",")?.length > 1 ? { [Op.in]: branchId?.toString()?.split(",")?.map(Number) } : branchId;
    if (departmentId) where.departmentId = departmentId;

    // Preserve date range filtering
    if (from)
      where.startTime = { [Op.gte]: moment(from).startOf("day").toDate() };
    if (to) where.endTime = { [Op.lte]: moment(to).endOf("day").toDate() };

    if (shiftId) where = { id: shiftId, organization_id: req.user.organization_id }

    // Fetch shifts (Main Query)
    const { rows: shifts, count }: any = await Shift.findAndCountAll({
      where,
      order: [
        [
          sortBy ? sortBy : "startTime",
          sortOrder && sortOrder != ""
            ? String(sortOrder).toUpperCase()
            : "ASC",
        ],
      ],
      raw: true,
      nest: true,
    });

    // Extract unique IDs for batch fetching
    const userIds = [
      ...new Set(shifts.map((s: any) => s.userId).filter(Boolean)),
    ];
    const roleIds: any = [
      ...new Set(shifts.map((s: any) => s.roleId).filter(Boolean)),
    ];
    const locationIds = [
      ...new Set(shifts.map((s: any) => s.branchId).filter(Boolean)),
    ];
    const departmentIds = [
      ...new Set(shifts.map((s: any) => s.departmentId).filter(Boolean)),
    ];
    const shiftIds = shifts.map((s: any) => s.id);

    // Determine platform type for role fetching
    const platformType = req.headers["platform-type"] as string;
    const isWebPlatform = !platformType || platformType === "web";

    // Batch fetch related data
    const [users, roles, locations, departments, swaps, drops] = await Promise.all([
      userIds.length ? getUsers(userIds) : [],
      roleIds.length ? (isWebPlatform ? getRolesMo(roleIds) : getRoles(roleIds)) : [],
      locationIds.length ? getBranchDetails(locationIds) : [],
      departmentIds.length ? getDepartmentDetails(departmentIds) : [],
      shiftIds.length
        ? Swap.findAll({
          where: {
            shiftId: shiftIds
          },
          raw: true,
          nest: true,
        })
        : [],
      shiftIds.length ? Drop.findAll({
        where: {
          shiftId: shiftIds
        },
        raw: true,
        nest: true,
      }) : [],
    ]);

    // Create lookup maps for faster access
    const userMap = Object.fromEntries(users.map((u: any) => [u.id, u]));
    const roleMap = Object.fromEntries(roles.map((r: any) => [r.id, r]));
    const locationMap = Object.fromEntries(
      locations.map((l: any) => [l.id, l]),
    );
    const departmentsMap = Object.fromEntries(
      departments.map((l: any) => [l.id, l]),
    );

    // Group swaps by shift ID
    const swapMap = swaps.reduce((acc: any, swap: any) => {
      if (!acc[swap.shiftId]) acc[swap.shiftId] = [];
      acc[swap.shiftId].push(swap);
      return acc;
    }, {});

    const dropMap = drops.reduce((acc: any, drop: any) => {
      if (!acc[drop.shiftId]) acc[drop.shiftId] = [];
      acc[drop.shiftId].push(drop);
      return acc;
    }, {});

    // Prepare to fetch availability data if requested
    let availabilityData: Record<string, any[]> = {};
    if (includeAvailability === "true") {
      // Apply the same filters to availability as applied to shifts
      const availabilityWhere: any = {
        status: AvailabilityStatus.active
      };

      // Apply user filter to availability
      if (userId) {
        availabilityWhere.userId = userId;
      } else if (isAdmin !== "true") {
        availabilityWhere.userId = req?.user?.id;
      }

      // Apply date range filters to availability
      if (from) {
        availabilityWhere.date = {
          ...availabilityWhere.date,
          [Op.gte]: moment(from).startOf("day").toDate()
        };
      }
      if (to) {
        availabilityWhere.date = {
          ...availabilityWhere.date,
          [Op.lte]: moment(to).endOf("day").toDate()
        };
      }

      // Fetch availability data
      try {
        const availabilities = await Availability.findAll({
          where: availabilityWhere,
          order: [["date", "ASC"]],
          raw: true,
        });

        // Group availabilities by date for easy lookup
        availabilityData = availabilities.reduce((acc: Record<string, any[]>, availability: any) => {
          const dateKey = moment(availability.date).format("YYYY-MM-DD");
          availability.user = userMap[availability.userId] || null;
          if (!acc[dateKey]) acc[dateKey] = [];
          acc[dateKey].push(availability);
          return acc;
        }, {});
      } catch (err) {
        // Log error but continue processing
        console.log(err);
      }
    }

    // Process shifts while preserving logic
    let processedShifts = await Promise.all(
      shifts.map(async (shift: any) => {
        shift.date = moment(shift.startTime).format("YYYY-MM-DD"); // Format date without moment
        shift.user = userMap[shift.userId] || null;
        shift.role = roleMap[shift.roleId] || null;
        shift.branch = locationMap[shift.branchId] || null;
        shift.department = departmentsMap[shift.departmentId] || null;

        if (includeAvailability === "true" && availabilityData[shift.date] && isDashboard !== "true") {
          shift.availability = availabilityData[shift.date];
        }
        // if (isDashboard !== "true") {
        // Process swap requests
        if (swapMap[shift.id]) {
          shift.swapRequests = await Promise.all(
            swapMap[shift.id].map(async (swap: any) => ({
              ...swap,
              from: userMap[swap.userId]
                ? userMap[swap.userId]
                : await getUser(swap.userId),
              to: userMap[swap.toUserId]
                ? userMap[swap.toUserId]
                : await getUser(swap.toUserId),
              role: roleMap[swap.roleId]
                ? roleMap[swap.roleId]
                : await getRoles(swap.roleId)?.then((roles: any) => roles && roles.length > 0 ? roles[0] : null),
            })),
          );
        } else {
          shift.swapRequests = [];
        }
        if (dropMap[shift.id]) {
          shift.drops = await Promise.all(
            dropMap[shift.id].map(async (drop: any) => ({
              ...drop,
              createdBy: userMap[drop.createdBy]
                ? userMap[drop.createdBy]
                : await getUser(drop.createdBy),
              updatedBy: userMap[drop.updatedBy]
                ? userMap[drop.updatedBy]
                : await getUser(drop.updatedBy),
            })),
          );
        }
        // }
        return shift;
      }),
    );

    // Handle dashboard view
    let dashboardData;
    if (isDashboard === "true") {
      // First group shifts by date
      const dateGroups = processedShifts.reduce((acc: any, shift: any) => {
        if (!acc[shift.date]) {
          acc[shift.date] = { date: shift.date, count: 0, shifts: [] };
        }
        acc[shift.date].count++;
        if (acc[shift.date].shifts.length < 5) {
          acc[shift.date].shifts.push(shift);
        }
        return acc;
      }, {});

      // Add availability data to each date group
      if (includeAvailability === "true") {
        Object.keys(availabilityData).forEach(date => {
          if (!dateGroups[date]) {
            // If there are no shifts for this date but there is availability data
            dateGroups[date] = {
              date,
              count: 0,
              shifts: [],
              availability: availabilityData[date]
            };
          } else {
            // If there are shifts for this date, add the availability data
            dateGroups[date].availability = availabilityData[date];
          }
        });
      }

      dashboardData = Object.values(dateGroups);
    }

    if (page && size) {
      processedShifts = processedShifts.slice((page - 1) * size, page * size);
    }

    // Preserve response logic
    const data = isDashboard === "true" ? dashboardData : processedShifts;

    return res.status(200).send({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      ...(req?.user?.rota_group_by ? { groupBy: req?.user?.rota_group_by } : {}),
      count,
      data: data,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

const deleteShift = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const findShift = await Shift.findOne({
      where: {
        id,
        organization_id: req.user.organization_id
      }
    });
    if (!findShift) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }

    const checkPermission = await validateModulePermission(req.user, req.user.organization_id, "rotas", ROLE_PERMISSIONS.DELETE, getPlatformFromRequest(req));

    if (req.user.id != findShift.userId) {
      if (!checkPermission) {
        return res.status(403).send({
          status: false,
          message: res.__("PERMISSION_DENIED"),
        });
      }
    }


    await Shift.update(
      {
        status: ShiftStatus.deleted,
      },
      {
        where: {
          id,
          organization_id: req.user.organization_id
        },
      }
    );

    return res.status(200).send({
      status: true,
      message: res.__("SHIFT_DELETED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send(
      {
        status: false,
        message: res.__("SOMETHING_WENT_WRONG"),
        error,
      },
    )
  }
};

const clearWeekShifts = async (req: Request, res: Response): Promise<any> => {
  try {
    const { startTime, endTime } = req.body;

    const checkPermission = await validateModulePermission(req.user, req.user.organization_id, "rotas", ROLE_PERMISSIONS.DELETE, getPlatformFromRequest(req));

    if (!checkPermission) {
      return res.status(403).send({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    // Ensure we have valid date strings
    if (!startTime || !endTime) {
      return res.status(400).send({
        status: false,
        message: res.__("START_TIME_AND_END_TIME_REQUIRED"),
      });
    }

    const startDate = moment(startTime).startOf("day").toDate();
    const endDate = moment(endTime).endOf("day").toDate();

    const where: any = {
      organization_id: req.user.organization_id,
      status: { [Op.ne]: ShiftStatus.deleted }, // Only find non-deleted shifts
      [Op.or]: [
        // Shifts that start within the date range
        {
          startTime: {
            [Op.between]: [startDate, endDate]
          }
        },
        // Shifts that end within the date range
        {
          endTime: {
            [Op.between]: [startDate, endDate]
          }
        },
        // Shifts that span the entire date range (start before, end after)
        {
          startTime: { [Op.lte]: startDate },
          endTime: { [Op.gte]: endDate }
        }
      ]
    };

    console.log('Clear Week Shifts - Query Parameters:', {
      startTime,
      endTime,
      startDate,
      endDate,
      organization_id: req.user.organization_id
    });

    const findShift = await Shift.findAll({
      where: where
    });

    console.log(`Found ${findShift.length} shifts to delete`);

    if (findShift.length > 0) {
      console.log('Sample shifts found:', findShift.slice(0, 3).map((shift: any) => ({
        id: shift.id,
        startTime: shift.startTime,
        endTime: shift.endTime,
        status: shift.status
      })));
    }

    if (!findShift.length) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }

    await Shift.update(
      {
        status: ShiftStatus.deleted,
      },
      {
        where: {
          id: { [Op.in]: findShift.map((shift: any) => shift.id) },
          organization_id: req.user.organization_id
        },
      }
    );

    return res.status(200).send({
      status: true,
      message: res.__("SHIFT_DELETED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send(
      {
        status: false,
        message: res.__("SOMETHING_WENT_WRONG"),
        error,
      },
    )
  }
};

const updateShift = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    let {
      userId,
      startTime,
      endTime,
      status,
      branchId,
      departmentId,
      role,
      isOpen,
      isPublished,
      acknowledged,
      notes,
    } = req.body;
    const { minutesBreak } = req.body

    const checkPermission = await validateModulePermission(req.user, req.user.organization_id, "rotas", ROLE_PERMISSIONS.EDIT, getPlatformFromRequest(req));

    if (!checkPermission) {
      return res.status(403).send({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    req.body.id = id;
    const validateShift = await commonShiftValidator(req);
    if (validateShift) {
      if (validateShift?.data?.id !== id) {
        return res.status(409).send({
          status: false,
          message: validateShift?.message,
        });
      }
    }

    const findShift = await Shift.findOne({
      where: {
        id,
        organization_id: req.user.organization_id
      }
    });
    if (!findShift) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }

    userId = userId ? userId : findShift.userId;
    startTime = startTime ? startTime : findShift.startTime;
    endTime = endTime ? endTime : findShift.endTime;
    status = status ? status : findShift.status;
    branchId = branchId ? branchId : findShift.branchId;
    departmentId = departmentId ? departmentId : findShift.departmentId;
    role = role ? role : findShift.role;
    notes = notes ? notes : findShift.notes;
    isOpen = isOpen ? true : false;
    isPublished = isPublished ? true : false;
    acknowledged = acknowledged ? true : false;

    const findUser = await getUser(userId);
    const updateObj = {
      userId,
      startTime: new Date(startTime),
      endTime: new Date(endTime),
      status,
      minutesBreak: minutesBreak,
      branchId,
      departmentId,
      roleId: role,
      isOpen: isOpen,
      isPublished: isPublished,
      isSwap: false,
      isDrop: false,
      acknowledged: acknowledged,
      updatedBy: req?.user?.id,
      notes: notes,
    };

    await Shift.update(updateObj, {
      where: {
        id,
        organization_id: req.user.organization_id
      },
    });

    await Swap.update({ status: 'deleted' }, {
      where: {
        shiftId: id,
        organization_id: req.user.organization_id
      }
    });

    await Drop.update({ status: 'deleted' }, {
      where: {
        shiftId: id,
        organization_id: req.user.organization_id
      }
    });

    const oldShift: any = {
      ...findShift.toJSON()
    };
    const newShift: any = {
      ...findShift.toJSON(),
      ...updateObj
    };

    if (moment(findShift.startTime).format("YYYY-MM-DD HH:mm:ss") !== moment(updateObj.startTime).format("YYYY-MM-DD HH:mm:ss")) {
      oldShift.startTime = moment(findShift.startTime).format("YYYY-MM-DD HH:mm:ss");
      oldShift.endTime = moment(findShift.endTime).format("YYYY-MM-DD HH:mm:ss");
      newShift.startTime = moment(updateObj.startTime).format("YYYY-MM-DD HH:mm:ss");
      newShift.endTime = moment(updateObj.endTime).format("YYYY-MM-DD HH:mm:ss");
    } else if (moment(findShift.endTime).format("YYYY-MM-DD HH:mm:ss") !== moment(updateObj.endTime).format("YYYY-MM-DD HH:mm:ss")) {
      oldShift.startTime = moment(findShift.startTime).format("YYYY-MM-DD HH:mm:ss");
      oldShift.endTime = moment(findShift.endTime).format("YYYY-MM-DD HH:mm:ss");
      newShift.startTime = moment(updateObj.startTime).format("YYYY-MM-DD HH:mm:ss");
      newShift.endTime = moment(updateObj.endTime).format("YYYY-MM-DD HH:mm:ss");
    }

    const changes = ShiftHistory.generateChanges(oldShift, newShift);
    await ShiftHistory.logChange(Number(id), req.user?.id, "UPDATE", changes, {
      ip: req?.headers?.["ip-address"] ? `${req?.headers?.["ip-address"]}` : "",
      userAgent: `${req?.headers?.["user-agent"]}`,
    });

    const notificationObj: any = {
      deviceId: await getUserDeviceId([findUser]),
      content: NOTIFICATIONS.SHIFT_UPDATE.CONTENT(
        findUser?.user_first_name,
        moment(startTime).format("YYYY-MM-DD HH:mm A"),
        moment(endTime).format("YYYY-MM-DD HH:mm A"),
      ),
      data: {
        notification_content: NOTIFICATIONS.SHIFT_UPDATE.CONTENT(
          findUser?.user_first_name,
          moment(startTime).format("YYYY-MM-DD HH:mm A"),
          moment(endTime).format("YYYY-MM-DD HH:mm A"),
        ),
        notification_subject: NOTIFICATIONS.SHIFT_UPDATE.HEADER,
        notification_image: null,
        to_user_id: userId,
        redirection_type: "shift_details",
        notification_status: "sent",
        notification_type: "shifts_add",
        reference_id: id,
        redirection_object: { id: id },
        from_user_id: req.user.id,
        created_by: req.user.id,
        updated_by: req.user.id
      },
      type: "shift",
      subject: NOTIFICATIONS.SHIFT_UPDATE.HEADER,
    };

    if (isPublished && acknowledged && !findShift.isPublished && !isOpen && notificationObj?.deviceId?.length > 0) {
      await createNotification(notificationObj?.data, req, notificationObj?.deviceId);
    }

    return res.status(200).send({
      status: true,
      message: res.__("SUCCESS_DATA_UPDATED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};
const setAsOpenShift = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;

    const checkPermission = await validateModulePermission(req.user, req.user.organization_id, "rotas", ROLE_PERMISSIONS.EDIT, getPlatformFromRequest(req));

    if (!checkPermission)
      return res
        .status(403)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });

    const findShift = await Shift.findByPk(id);
    if (!findShift) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }

    req.body = JSON.parse(JSON.stringify(findShift));
    req.body.startTime = moment(findShift.startTime).format(
      "YYYY-MM-DD HH:mm:ss",
    );
    req.body.endTime = moment(findShift.endTime).format("YYYY-MM-DD HH:mm:ss");

    const validateShift = await commonShiftValidator(req);
    if (validateShift) {
      if (validateShift?.data?.id !== id) {
        return res.status(409).send({
          status: false,
          message: validateShift?.message,
        });
      }
    }

    await Shift.update(
      {
        userId: null,
        isOpen: true,
        updatedBy: req?.user?.id,
      },
      {
        where: {
          id,
        },
      },
    );

    const changes = ShiftHistory.generateChanges(findShift, {
      ...findShift,
      userId: null,
      isOpen: true,
      updatedBy: req?.user?.id,
    });
    await ShiftHistory.logChange(Number(id), req.user?.id, "OPEN", changes, {
      ip: req?.headers?.["ip-address"] ? `${req?.headers?.["ip-address"]}` : "",
      userAgent: `${req?.headers?.["user-agent"]}`,
    });

    return res.status(200).send({
      status: true,
      message: res.__("SHIFT_SET_TO_OPEN"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

const dropShift = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const findShift = await Shift.findByPk(id);
    if (!findShift || (findShift && findShift.userId != req.user.id)) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }

    const findPreviousData = await Drop.findOne({
      where: {
        shiftId: id,
        organization_id: req.user.organization_id
      },
    });
    if (findPreviousData && findPreviousData.status === "pending") {
      return res.status(409).send({
        status: false,
        message: res.__("DROP_ALREADY_REQUESTED"),
      });
    }
    if (findPreviousData && findPreviousData.status === "deleted") {
      return res.status(409).send({
        status: false,
        message: res.__("DROP_ALREADY_DENIED"),
      });
    }


    await Shift.update(
      {
        isDropped: true,
        updatedBy: req?.user?.id,
      },
      {
        where: {
          id,
          organization_id: req.user.organization_id
        },
      },
    );

    await Drop.create({
      shiftId: id,
      userId: req.user.id,
      createdBy: req.user.id,
      reason: reason,
      organization_id: req.user.organization_id
    })

    const changes = ShiftHistory.generateChanges(findShift, {
      ...findShift,
      isDropped: true,
      updatedBy: req?.user?.id,
    });
    await ShiftHistory.logChange(Number(id), req.user?.id, "DROP", changes, {
      ip: req?.headers?.["ip-address"] ? `${req?.headers?.["ip-address"]}` : "",
      userAgent: `${req?.headers?.["user-agent"]}`,
    });

    const findUser = await getUser(findShift.createdBy);

    const notificationObj = {
      deviceId: await getUserDeviceId([findUser]),
      content: NOTIFICATIONS.SHIFT_DROP_REQUEST.CONTENT(
        req.user?.user_first_name,
        moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
        moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
      ),
      data: {
        notification_content: NOTIFICATIONS.SHIFT_DROP_REQUEST.CONTENT(
          req.user?.user_first_name,
          moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
          moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
        ),
        notification_subject: NOTIFICATIONS.SHIFT_DROP_REQUEST.HEADER,
        notification_image: null,
        to_user_id: findUser.id,
        redirection_type: "shift_details",
        notification_type: "shifts_drop",
        notification_status: "sent",
        reference_id: id,
        redirection_object: { id: id },
        from_user_id: req.user.id,
        created_by: req.user.id,
        updated_by: req.user.id
      },
      type: "shift",
      subject: NOTIFICATIONS.SHIFT_DROP_REQUEST.HEADER,
    };
    if (notificationObj?.deviceId?.length > 0) {
      await createNotification(notificationObj?.data, req, notificationObj?.deviceId);
    }
    return res.status(200).send({
      status: true,
      message: res.__("SHIFT_DROPPED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

const dropAction = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    const findShift = await Shift.findOne({
      where: {
        id,
        organization_id: req.user.organization_id
      }
    });
    if (!findShift) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }

    await Drop.update(
      {
        status,
        updatedBy: req?.user?.id,
      },
      {
        where: {
          shiftId: id,
          organization_id: req.user.organization_id
        },
      });

    const findUser = await getUser(findShift.userId);

    const notificationObj = {
      deviceId: await getUserDeviceId([findUser]),
      content: NOTIFICATIONS.SHIFT_DROP_REJECT.CONTENT(
        req.user?.user_first_name,
        moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
        moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
      ),
      data: {
        notification_content: NOTIFICATIONS.SHIFT_DROP_REJECT.CONTENT(
          req.user?.user_first_name,
          moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
          moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
        ),
        notification_subject: NOTIFICATIONS.SHIFT_DROP_REJECT.HEADER,
        notification_image: null,
        to_user_id: findUser.id,
        redirection_type: "shift_details",
        notification_type: "shifts_drop",
        notification_status: "sent",
        reference_id: id,
        redirection_object: { id: id },
        from_user_id: req.user.id,
        created_by: req.user.id,
        updated_by: req.user.id
      },
      type: "shift",
      subject: NOTIFICATIONS.SHIFT_DROP_REJECT.HEADER,
    };


    if (status == "active") {
      notificationObj.content = NOTIFICATIONS.SHIFT_DROP_ACCEPT.CONTENT(
        req.user?.user_first_name,
        moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
        moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
      )
      notificationObj.data.notification_content = NOTIFICATIONS.SHIFT_DROP_ACCEPT.CONTENT(
        req.user?.user_first_name,
        moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
        moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
      )
      notificationObj.subject = NOTIFICATIONS.SHIFT_DROP_ACCEPT.HEADER
      notificationObj.data.notification_subject = NOTIFICATIONS.SHIFT_DROP_ACCEPT.HEADER

      const updateData = {
        isDropped: false,
        isPublished: false,
        isOpen: true,
        isSwap: false,
        userId: null,
        updatedBy: req?.user?.id,
      }
      const changes = ShiftHistory.generateChanges(findShift, {
        ...findShift,
        ...updateData
      });
      await ShiftHistory.logChange(Number(id), req.user?.id, "DROP", changes, {
        ip: req?.headers?.["ip-address"] ? `${req?.headers?.["ip-address"]}` : "",
        userAgent: `${req?.headers?.["user-agent"]}`,
      });
      await Shift.update(
        updateData,
        {
          where: {
            id,
            organization_id: req.user.organization_id
          },
        },
      );
    } else {
      await Shift.update(
        {
          isDropped: true,
          updatedBy: req?.user?.id,
        },
        {
          where: {
            id,
            organization_id: req.user.organization_id
          },
        },
      );
    }

    if (notificationObj?.deviceId?.length > 0) {
      await createNotification(notificationObj?.data, req, notificationObj?.deviceId);
    }

    return res.status(200).send({
      status: true,
      message: status == "active" ? res.__("DROP_SHIFT_APPROVED") : res.__("DROP_SHIFT_REJECTED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
}

const checkShiftAvailability = async (
  req: Request,
  res: Response,
): Promise<any> => {
  try {
    const validateShift = await commonShiftValidator(req);
    if (validateShift) {
      return res.status(409).send({
        status: false,
        message: validateShift?.message,
        data: validateShift?.data,
      });
    }

    return res.status(200).send({
      status: true,
      message: res.__("SHIFT_AVAILABLE"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

const publishUnpublishShift = async (
  req: Request,
  res: Response,
): Promise<any> => {
  try {

    const checkPermission = await validateModulePermission(req.user, req.user.organization_id, "rotas", ROLE_PERMISSIONS.EDIT, getPlatformFromRequest(req));

    if (!checkPermission)
      return res
        .status(403)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });

    const { ids } = req.body;
    const findShift = await Shift.findAll({
      where: { id: { [Op.in]: ids } },
    });
    if (!findShift.length) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }
    const { isPublished } = req.body;
    await Shift.update(
      {
        isPublished: isPublished == true ? true : false,
        updatedBy: req?.user?.id,
      },
      { where: { id: { [Op.in]: findShift?.map((item: any) => item.id) } } },
    );

    for (const s of findShift) {

      const findUser = await getUser(s?.userId);

      const notificationObj = {
        deviceId: await getUserDeviceId([findUser]),
        content: NOTIFICATIONS.SHIFT_UPDATE.CONTENT(
          findUser?.user_first_name,
          moment(s.startTime).format("YYYY-MM-DD HH:mm A"),
          moment(s.endTime).format("YYYY-MM-DD HH:mm A"),
        ),
        data: {
          notification_content: NOTIFICATIONS.SHIFT_UPDATE.CONTENT(
            findUser?.user_first_name,
            moment(s.startTime).format("YYYY-MM-DD HH:mm A"),
            moment(s.endTime).format("YYYY-MM-DD HH:mm A"),
          ),
          notification_subject: NOTIFICATIONS.SHIFT_UPDATE.HEADER,
          notification_image: null,
          to_user_id: s.userId,
          redirection_type: "shift_details",
          notification_status: "sent",
          notification_type: "shifts_add",
          reference_id: s.id,
          redirection_object: { id: s.id },
          from_user_id: req.user.id,
          created_by: req.user.id,
          updated_by: req.user.id
        },
        type: "shift",
        subject: NOTIFICATIONS.SHIFT_UPDATE.HEADER,
      };

      if (findUser) {
        if (isPublished && s.acknowledged && !s.isPublished && !s.isOpen && notificationObj?.deviceId?.length > 0) {
          await createNotification(notificationObj?.data, req, notificationObj?.deviceId);
        }
      }

      const changes = ShiftHistory.generateChanges(s, {
        ...s,
        isPublished: isPublished == true ? true : false,
        updatedBy: req?.user?.id,
      });
      await ShiftHistory.logChange(
        s.id,
        req.user?.id,
        isPublished == true ? "PUBLISH" : "UNPUBLISH",
        changes,
        {
          ip: req?.headers?.["ip-address"]
            ? `${req?.headers?.["ip-address"]}`
            : "",
          userAgent: `${req?.headers?.["user-agent"]}`,
        },
      );
    }

    return res.status(200).send({
      status: true,
      message:
        isPublished == true
          ? res.__("SHIFT_PUBLISHED")
          : res.__("SHIFT_UNPUBLISHED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

const swapShift = async (req: Request, res: Response): Promise<any> => {
  try {
    req.body.fromUserId = req.body.userId;
    req.body.userId = req.body.toUserId;
    const { shiftId, toUserId, roleId } = req.body;

    const findShift = await Shift.findOne({
      where: {
        id: shiftId,
        organization_id: req.user.organization_id
      },
      raw: true
    });
    if (!findShift) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }
    req.body.startTime = new Date(findShift.startTime);
    req.body.endTime = new Date(findShift.endTime);

    const validateShift = await commonShiftValidator(req);
    if (validateShift) {
      return res.status(409).send({
        status: false,
        message: validateShift?.message,
        data: validateShift?.data,
      });
    }

    if (roleId) {
      const findRole = await getRoles([roleId]);
      if (!findRole.length) {
        return res.status(404).send({
          status: false,
          message: res.__("ROLE_NOT_FOUND"),
        });
      }
    }

    const existSwap = await Swap.findOne({
      where: {
        shiftId,
        toUserId,
        status: { [Op.not]: ShiftStatus.deleted },
        organization_id: req.user.organization_id
      },
    });

    if (existSwap) {
      return res.status(409).send({
        status: false,
        message: res.__("SWAP_EXISTS"),
      });
    }

    await Shift.update(
      { isSwap: 1 },
      {
        where: {
          id: shiftId,
          organization_id: req.user.organization_id
        }
      }
    );

    const changes = ShiftHistory.generateChanges(findShift, {
      ...findShift,
      isSwap: 1,
    });
    await ShiftHistory.logChange(findShift.id, req.user?.id, "SWAP", changes, {
      ip: req?.headers?.["ip-address"] ? `${req?.headers?.["ip-address"]}` : "",
      userAgent: `${req?.headers?.["user-agent"]}`,
    });

    const newSwap = await Swap.create({
      userId: req.body.fromUserId,
      toUserId,
      shiftId,
      roleId,
      createdBy: req?.user?.id,
      updatedBy: req?.user?.id,
      organization_id: req.user.organization_id
    });

    const findToUser = await getUser(toUserId);

    const notificationObj = {
      deviceId: await getUserDeviceId([findToUser]),
      content: NOTIFICATIONS.SHIFT_SWAP.CONTENT(
        req.user?.user_first_name,
        moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
        moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
        findToUser?.user_first_name,
      ),
      data: {
        notification_content: NOTIFICATIONS.SHIFT_SWAP.CONTENT(
          req.user?.user_first_name,
          moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
          moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
          findToUser?.user_first_name,
        ),
        notification_subject: NOTIFICATIONS.SHIFT_SWAP.HEADER,
        notification_image: null,
        to_user_id: findToUser.id,
        redirection_type: "shift_details",
        notification_type: "shifts_cover",
        notification_status: "sent",
        reference_id: shiftId,
        redirection_object: { id: shiftId },
        from_user_id: req.user.id,
        created_by: req.user.id,
        updated_by: req.user.id
      },
      type: "shift",
      subject: NOTIFICATIONS.SHIFT_SWAP.HEADER,
    };

    if (notificationObj?.deviceId?.length > 0) {
      await createNotification(notificationObj?.data, req, notificationObj?.deviceId);
    }

    return res.status(201).send({
      status: true,
      message: res.__("SWAP_CREATED"),
      data: newSwap,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

const swapActions = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id, status } = req.body;
    const findSwap = await Swap.findOne({
      where: {
        id,
        organization_id: req.user.organization_id
      }
    });
    if (!findSwap) {
      return res.status(404).send({
        status: false,
        message: res.__("SWAP_NOT_FOUND"),
      });
    }

    const updateObj: any = {
      updatedBy: req?.user?.id,
    }

    const isAdmin = await permittedForAdminMO(req.user?.id, req.user.organization_id, [ROLE_CONSTANT.SUPER_ADMIN, ROLE_CONSTANT.ADMIN, ROLE_CONSTANT.DIRECTOR, ROLE_CONSTANT.HR, ROLE_CONSTANT.BRANCH_MANAGER])


    const findUser = await getUser(findSwap.userId);
    const findToUser = await getUser(findSwap.toUserId);

    const findShift = await Shift.findOne({
      where: {
        id: findSwap.shiftId,
        organization_id: req.user.organization_id
      }
    });
    const adminUsers = await getUser(findShift.createdBy);

    if (findSwap.userId != req?.user?.id) {
      if (isAdmin && findToUser?.id != req?.user?.id) {
        if (findSwap.status == ShiftStatus.pending) {
          return res.status(400).send({
            status: false,
            message: `${findToUser?.user_first_name} must first reply to this swap request`,
          });
        }
        updateObj.adminStatus = status;
      } else {
        updateObj.status = status
      }
    } else {
      updateObj.status = status
    }

    await Swap.update(
      updateObj,
      {
        where: {
          id,
          organization_id: req.user.organization_id
        }
      },
    );

    let content = NOTIFICATIONS.SHIFT_SWAP_ACCEPT.CONTENT(
      findToUser?.user_first_name,
      moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
      moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
      findUser?.user_first_name,
    );
    let header = NOTIFICATIONS.SHIFT_SWAP_ACCEPT.HEADER;
    let deviceId = findToUser?.webAppToken
      ? findToUser?.webAppToken
      : findToUser?.appToken;
    let notificationUser = findToUser.id;
    if (status == ShiftStatus.active) {
      if ((isAdmin && findSwap.status == ShiftStatus.active) || ((!isAdmin && findSwap.adminStatus == ShiftStatus.active))) {
        await Shift.update(
          { userId: findSwap.toUserId, roleId: findSwap.roleId },
          { where: { id: findSwap.shiftId } },
        );
      }

      if (isAdmin) {
        // Send notification to both fromUser and toUser when admin accepts
        const notificationUsers = [findUser, findToUser];
        for (const user of notificationUsers) {
          const notificationObj = {
            deviceId: await getUserDeviceId([user]),
            content: content,
            data: {
              notification_content: content,
              notification_subject: header,
              notification_image: null,
              to_user_id: user.id,
              redirection_type: "shift_details",
              notification_type: "shifts_cover",
              notification_status: "sent",
              reference_id: findSwap.shiftId,
              redirection_object: { id: findSwap.shiftId },
              from_user_id: req.user.id,
              created_by: req.user.id,
              updated_by: req.user.id,
              createdAt: moment().format("YYYY-MM-DD HH:mm:ss"),
              updatedAt: moment().format("YYYY-MM-DD HH:mm:ss")
            },
            type: "shift",
            subject: header,
          };
          if (notificationObj?.deviceId?.length > 0) {
            await createNotification(notificationObj?.data, req, notificationObj?.deviceId);
          }
        }
      } else {
        // Send notification to admin users and requester when toUser accepts
        const notificationUsers = [...adminUsers, findUser];
        for (const user of notificationUsers) {
          const notificationObj = {
            deviceId: await getUserDeviceId([user]),
            content: content,
            data: {
              notification_content: content,
              notification_subject: header,
              notification_image: null,
              to_user_id: user.id,
              redirection_type: "shift_details",
              notification_type: "shifts_cover",
              notification_status: "sent",
              reference_id: findSwap.shiftId,
              redirection_object: { id: findSwap.shiftId },
              from_user_id: req.user.id,
              created_by: req.user.id,
              updated_by: req.user.id,
              createdAt: moment().format("YYYY-MM-DD HH:mm:ss"),
              updatedAt: moment().format("YYYY-MM-DD HH:mm:ss")
            },
            type: "shift",
            subject: header,
          };
          if (notificationObj?.deviceId?.length > 0) {
            await createNotification(notificationObj?.data, req, notificationObj?.deviceId);
          }
        }
      }
    } else {
      if (isAdmin || findSwap.userId == req.user?.id) {
        await Shift.update(
          { userId: findSwap.userId, isSwap: false },
          { where: { id: findSwap.shiftId } },
        );
      }
      content = NOTIFICATIONS.SHIFT_SWAP_REJECT.CONTENT(
        findUser?.user_first_name,
        moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
        moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
        findToUser?.user_first_name,
      );
      header = NOTIFICATIONS.SHIFT_SWAP_REJECT.HEADER;
      deviceId = await getUserDeviceId([findUser]);
      notificationUser = findUser.id;

      const notificationObj = {
        deviceId: deviceId,
        content: content,
        data: {
          notification_content: content,
          notification_subject: header,
          notification_image: null,
          to_user_id: notificationUser,
          redirection_type: "shift_details",
          notification_type: "shifts_cover",
          notification_status: "sent",
          reference_id: findSwap.shiftId,
          redirection_object: { id: findSwap.shiftId },
          from_user_id: req.user.id,
          created_by: req.user.id,
          updated_by: req.user.id,
          createdAt: moment().format("YYYY-MM-DD HH:mm:ss"),
          updatedAt: moment().format("YYYY-MM-DD HH:mm:ss")
        },
        type: "shift",
        subject: header,
      };

      if (req.user.id != findShift.userId) {
        if (notificationObj?.deviceId?.length > 0) {
          await createNotification(notificationObj?.data, req, notificationObj?.deviceId);
        }
      }
    }

    return res.status(200).send({
      status: true,
      message:
        status == ShiftStatus.active
          ? res.__("SWAP_ACCEPTED")
          : res.__("SWAP_REJECTED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};
const getShiftHistory = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;

    const findShift = await Shift.findOne({
      where: {
        id,
        organization_id: req.user.organization_id
      },
      raw: true,
      nest: true
    });
    if (!findShift) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }

    const findShiftHistory = await ShiftHistory.findAll({
      where: {
        shiftId: id,
      },
      order: [["createdAt", "DESC"]],
      raw: true,
      nest: true
    });

    await Promise.all(
      findShiftHistory.map(async (history: any) => {
        history.changes = history.changes ? JSON.parse(JSON.stringify(history.changes)) : {};
        history.metadata = history.metadata ? JSON.parse(JSON.stringify(history.metadata)) : {};
        history.user = await getUser(history.userId);
        return history;
      }),
    );

    return res.status(200).send({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      data: {
        ...findShift,
        history: findShiftHistory,
      },
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

const copyShiftsRange = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      fromStartDate,
      fromEndDate,
      toStartDate,
      toEndDate,
      selectedEmployees,
      copyDaysOff,
      shiftClashStrategy
    } = req.body;

    if (!fromStartDate || !fromEndDate || !toStartDate || !toEndDate) {
      return res.status(400).send({ status: false, message: res.__("INVALID_DATE_RANGE") });
    }

    if (!shiftClashStrategy || !["clear", "skip", "overwrite", "openshift"].includes(shiftClashStrategy)) {
      return res.status(400).send({ status: false, message: res.__("INVALID_SHIFT_CLASH_STRATEGY") });
    }

    const fromStart = moment(fromStartDate).startOf("day");
    const fromEnd = moment(fromEndDate).endOf("day");
    const toStart = moment(toStartDate).startOf("day");
    const toEnd = moment(toEndDate).endOf("day");

    if (fromStart > fromEnd || toStart > toEnd) {
      return res.status(400).send({ status: false, message: res.__("INVALID_DATE_RANGE") });
    }

    const queryConditions: any = {
      startTime: { [Op.between]: [fromStart.toDate(), fromEnd.toDate()] },
      status: ShiftStatus.active,
      organization_id: req.user.organization_id
    };

    if (selectedEmployees?.length) {
      queryConditions.userId = { [Op.in]: selectedEmployees };
    }

    if (shiftClashStrategy === "clear") {
      await Shift.update(
        { status: ShiftStatus.deleted },
        {
          where: {
            [Op.or]: [
              {
                startTime: {
                  [Op.between]: [toStart.toDate(), toEnd.toDate()],
                },
              },
              {
                endTime: {
                  [Op.between]: [toStart.toDate(), toEnd.toDate()],
                },
              },
              {
                startTime: { [Op.lte]: toStart.toDate() },
                endTime: { [Op.gte]: toEnd.toDate() },
              },
            ],
            status: { [Op.ne]: ShiftStatus.deleted },
            organization_id: req.user.organization_id,
            ...(selectedEmployees?.length && { userId: { [Op.in]: selectedEmployees } })
          }
        }
      );
    }


    const shifts = await Shift.findAll({ where: queryConditions });

    const fromDays = fromEnd.diff(fromStart, "days") + 1;
    const toDays = toEnd.diff(toStart, "days") + 1;

    if (fromDays > toDays) {
      return res.status(400).send({
        status: false,
        message: res.__("TO_DATE_RANGE_MUST_BE_GREATER_THAN_FROM_DATE_RANGE"),
      });
    }

    const sourceDayMap: Record<string, Shift[]> = {};
    for (const shift of shifts) {
      const key = moment(shift.startTime).format("YYYY-MM-DD");
      if (!sourceDayMap[key]) sourceDayMap[key] = [];
      sourceDayMap[key].push(shift);
    }

    const results: any = {
      copiedShifts: 0,
      skippedShifts: 0,
      overwrittenShifts: 0,
      openShifts: 0,
      conflictDetails: []
    };

    for (let i = 0; i < Math.min(fromDays, toDays); i++) {
      const currentFromDay = fromStart.clone().add(i, "days").format("YYYY-MM-DD");
      const currentToDayStart = toStart.clone().add(i, "days");

      const shiftsForDay = sourceDayMap[currentFromDay] || [];

      for (const shift of shiftsForDay) {
        const duration = moment(shift.endTime).diff(moment(shift.startTime), "milliseconds");
        const timeOfDay = moment(shift.startTime).diff(moment(currentFromDay).startOf("day"), "milliseconds");

        const newStartTime = currentToDayStart.clone().startOf("day").add(timeOfDay, "milliseconds").toDate();
        const newEndTime = moment(newStartTime).add(duration, "milliseconds").toDate();

        let shouldCreateShift = true;
        const shiftToCreate = { ...shift.toJSON() };

        if (shiftClashStrategy !== "clear") {
          const conflictQuery: any = {
            [Op.or]: [
              { startTime: { [Op.between]: [newStartTime, newEndTime] } },
              { endTime: { [Op.between]: [newStartTime, newEndTime] } },
              { startTime: { [Op.lte]: newStartTime }, endTime: { [Op.gte]: newEndTime } }
            ],
            status: ShiftStatus.active,
            isDropped: false,
            organization_id: req.user.organization_id
          };

          const existingShift = await Shift.findOne({ where: conflictQuery });

          if (existingShift) {
            const conflictDetail = {
              date: moment(newStartTime).format("YYYY-MM-DD"),
              time: `${moment(newStartTime).format("HH:mm")} - ${moment(newEndTime).format("HH:mm")}`,
              originalUserId: shift.userId,
              conflictingShiftId: existingShift.id,
              strategy: shiftClashStrategy
            };

            if (shiftClashStrategy === "skip") {
              results.skippedShifts++;
              results.conflictDetails.push({ ...conflictDetail, action: "skipped" });
              shouldCreateShift = false;
            } else if (shiftClashStrategy === "openshift") {
              const conflictingShifts = await Shift.findAll({ where: conflictQuery });

              if (conflictingShifts.length > 0) {
                for (const conflict of conflictingShifts) {
                  // Avoid updating if already open
                  if (!conflict.isOpen || conflict.userId !== null) {
                    await conflict.update({
                      userId: null,
                      isOpen: true,
                      updatedBy: req.user.id
                    });

                    results.openShifts++;
                    results.conflictDetails.push({
                      ...conflictDetail,
                      conflictingShiftId: conflict.id,
                      action: "converted_existing_to_open"
                    });

                    // Optionally log history
                    const changes = ShiftHistory.generateChanges(
                      { userId: conflict.userId, isOpen: conflict.isOpen },
                      { userId: null, isOpen: true }
                    );
                    await ShiftHistory.logChange(conflict.id, req.user?.id, "UPDATE", changes, {
                      ip: req?.headers?.["ip-address"] ? `${req?.headers?.["ip-address"]}` : "",
                      userAgent: `${req?.headers?.["user-agent"]}`,
                    });
                  }
                }
              }

              // Create the new shift as-is
              shouldCreateShift = true;
            } else if (shiftClashStrategy === "overwrite") {
              await Shift.update(
                { status: ShiftStatus.deleted },
                {
                  where: {
                    id: existingShift.id,
                    organization_id: req.user.organization_id
                  }
                }
              );
              results.overwrittenShifts++;
              results.conflictDetails.push({ ...conflictDetail, action: "overwritten" });
            }
          }
        }

        if (shouldCreateShift) {
          const newShift = await Shift.create({
            ...shiftToCreate,
            id: undefined,
            startTime: newStartTime,
            endTime: newEndTime,
            organization_id: req.user.organization_id,
            createdBy: req.user.id,
            updatedBy: req.user.id,
            createdAt: undefined,
            updatedAt: undefined,
          });

          results.copiedShifts++;

          const changes = ShiftHistory.generateChanges({}, newShift);
          await ShiftHistory.logChange(newShift.id, req.user?.id, "CREATE", changes, {
            ip: req?.headers?.["ip-address"] ? `${req?.headers?.["ip-address"]}` : "",
            userAgent: `${req?.headers?.["user-agent"]}`,
          });
        }
      }

      if (copyDaysOff) {
        const dayOffs = await DayOff.findAll({
          where: {
            date: {
              [Op.between]: [fromStart.toDate(), fromEnd.toDate()],
            },
            organization_id: req.user.organization_id,
            ...(selectedEmployees?.length && { userId: { [Op.in]: selectedEmployees } })
          }
        });

        for (const dayOff of dayOffs) {
          const newDate = currentToDayStart
            .clone()
            .add(moment(dayOff.date).diff(fromStart))
            .toDate();

          await DayOff.create({
            ...dayOff.toJSON(),
            id: undefined,
            date: newDate,
            organization_id: req.user.organization_id,
            createdAt: undefined,
            updatedAt: undefined,
          });
        }
      }
    }

    return res.status(200).send({
      status: true,
      message: res.__("SHIFTS_COPIED_SUCCESSFULLY"),
      // summary: results
    });

  } catch (err) {
    console.error("Shift Copy Error", err);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: err,
    });
  }
};


const checkCopyShiftTimeConflict = async (
  req: Request,
  res: Response,
): Promise<any> => {
  try {
    const { fromStartDate, fromEndDate, toStartDate, toEndDate } = req.body;

    if (!fromStartDate || !fromEndDate || !toStartDate || !toEndDate) {
      return res.status(400).send({
        status: false,
        message: res.__("INVALID_DATE_RANGE"),
      });
    }

    const fromStart = moment(fromStartDate).startOf("day");
    const fromEnd = moment(fromEndDate).endOf("day");
    const toStart = moment(toStartDate).startOf("day");
    const toEnd = moment(toEndDate).endOf("day");

    if (fromStart > fromEnd || toStart > toEnd) {
      return res.status(400).send({
        status: false,
        message: res.__("INVALID_DATE_RANGE"),
      });
    }

    const daysDifferenceFrom = fromEnd.diff(fromStart, "days");
    const daysDifferenceTo = toEnd.diff(toStart, "days");

    if (daysDifferenceTo < daysDifferenceFrom) {
      return res.status(400).send({
        status: false,
        message: res.__("INVALID_DATE_RANGE"),
      });
    }

    const shifts = await Shift.findAll({
      where: {
        startTime: {
          [Op.between]: [fromStart.toDate(), fromEnd.toDate()],
        },
      },
    });

    if (!shifts.length) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }

    const shiftConflicts = [];
    let message = "";
    for (let i = 0; i <= daysDifferenceTo; i++) {
      const currentToStart = toStart.clone().add(i, "days");

      for (let j = 0; j < shifts.length; j++) {
        const shift = shifts[j];
        const duration = moment(shift.endTime).diff(
          moment(shift.startTime),
          "milliseconds",
        );
        const newStartTime = currentToStart
          .clone()
          .add(moment(shift.startTime).diff(fromStart))
          .toDate();
        const newEndTime = moment(newStartTime)
          .add(duration, "milliseconds")
          .toDate();

        const existingShift = await commonShiftValidator({
          ...req,
          body: {
            ...req.body,
            startTime: newStartTime,
            endTime: newEndTime,
            isOpen: shift.isOpen,
            userId: shift.userId,
          },
        });

        if (existingShift) {
          message = existingShift.message;
          if (existingShift?.data) shiftConflicts.push(existingShift?.data);
        }
      }
    }

    if (shiftConflicts.length) {
      return res.status(200).send({
        status: true,
        message: message ? message : res.__("SHIFTS_TIME_CONFLICT"),
        data: shiftConflicts,
      });
    }

    return res.status(200).send({
      status: true,
      message: res.__("SHIFTS_NO_CONFLICT"),
    });
  } catch (err) {
    console.error(err);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: err,
    });
  }
};

const claimOpenShift = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params
    const userId = req.user.id

    const findShift = await Shift.findOne({
      where: {
        id,
        organization_id: req.user.organization_id
      }, raw: true, nest: true
    })

    if (!findShift) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }

    const existingShift = await commonShiftValidator({
      ...req,
      body: {
        ...findShift,
        userId: userId,
      },
    });

    if (existingShift) {
      return res.status(200).send({
        status: false,
        message: existingShift.message,
      });
    }

    await Shift.update({
      isClaim: true,
      userId: userId,
      isOpen: false
    }, {
      where: {
        id,
        organization_id: req.user.organization_id
      }
    })

    return res.status(200).send({
      status: true,
      message: res.__("SHIFT_CLAIMED"),
    })

  } catch (err) {
    console.error(err);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: err,
    });
  }
}

const exportShiftsToExcel = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      userId,
      startDate,
      endDate,
      role,
      branchId,
      departmentId,
      isAdmin,
      isOpen,
      sortBy,
      sortOrder,
      status,
    }: any = req.query;

    if (!startDate || !endDate) {
      return res.status(400).send({
        status: false,
        message: res.__("START_DATE_AND_END_DATE_REQUIRED"),
      });
    }

    let where: any = {
      status: status ? status : { [Op.not]: ShiftStatus.deleted },
    };

    const checkPermission = await permittedForAdminMO(req.user?.id, req.user.organization_id, [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.HR,
    ]);

    if (!checkPermission) {
      return res.status(403).send({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    if (userId && role) {
      where = {
        ...where,
        [Op.or]: [
          {
            userId: userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId,
            roleId: role?.toString()?.split(",")?.length > 1 ? { [Op.in]: role?.toString()?.split(",")?.map(Number) } : role,
          },
          {
            userId: userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId,
            roleId: { [Op.not]: role },
          },
          {
            roleId: role?.toString()?.split(",")?.length > 1 ? { [Op.in]: role?.toString()?.split(",")?.map(Number) } : role,
            isOpen: true,
          }
        ]
      }
    } else if (!userId && role) {
      where = {
        ...where,
        roleId: role?.toString()?.split(",")?.length > 1 ? { [Op.in]: role?.toString()?.split(",")?.map(Number) } : role
      }
    } else if (userId && !role) {
      if (isAdmin === "true") {
        where = {
          ...where,
          userId: userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId
        }
      } else {
        where = {
          ...where,
          userId: req?.user?.id
        }
      }
    }

    // Admin and user filtering logic
    if (isAdmin === "true") {
      if (userId && !role) where.userId = userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId;
    }

    if (isAdmin != "true") {
      where.isPublished = 1;
    }

    // Open shift condition
    if (isOpen === "true") {
      where.isOpen = true;
      where.userId = { [Op.is]: userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId ? userId : null };
    }

    // Role and location filtering
    if (branchId) where.branchId = branchId?.toString()?.split(",")?.length > 1 ? { [Op.in]: branchId?.toString()?.split(",")?.map(Number) } : branchId;
    if (departmentId) where.departmentId = departmentId;

    // Date range filtering
    where.startTime = { [Op.gte]: moment(startDate).startOf("day").toDate() };
    where.endTime = { [Op.lte]: moment(endDate).endOf("day").toDate() };

    // Fetch shifts
    const { rows: shifts, count }: any = await Shift.findAndCountAll({
      where,
      order: [
        [
          sortBy ? sortBy : "startTime",
          sortOrder && sortOrder != ""
            ? String(sortOrder).toUpperCase()
            : "ASC",
        ],
      ],
      raw: true,
      nest: true,
    });

    if (!shifts.length) {
      return res.status(404).send({
        status: false,
        message: res.__("NO_SHIFTS_FOUND"),
      });
    }

    // Extract unique IDs for batch fetching
    const userIds = [
      ...new Set(shifts.map((s: any) => s.userId).filter(Boolean)),
    ];
    const roleIds = [
      ...new Set(shifts.map((s: any) => s.roleId).filter(Boolean)),
    ];
    const locationIds = [
      ...new Set(shifts.map((s: any) => s.branchId).filter(Boolean)),
    ];
    const departmentIds = [
      ...new Set(shifts.map((s: any) => s.departmentId).filter(Boolean)),
    ];

    // Batch fetch related data
    const [users, roles, locations, departments] = await Promise.all([
      userIds.length ? getUsers(userIds) : [],
      roleIds.length ? getRoles(roleIds) : [],
      locationIds.length ? getBranchDetails(locationIds) : [],
      departmentIds.length ? getDepartmentDetails(departmentIds) : [],
    ]);

    // Create lookup maps for faster access
    const userMap = Object.fromEntries(users.map((u: any) => [u.id, u]));
    const roleMap = Object.fromEntries(roles.map((r: any) => [r.id, r]));
    const locationMap = Object.fromEntries(
      locations.map((l: any) => [l.id, l]),
    );
    const departmentsMap = Object.fromEntries(
      departments.map((l: any) => [l.id, l]),
    );

    // Process shifts and add related data
    const processedShifts = shifts.map((shift: any) => {
      const user = userMap[shift.userId] || null;
      const role = roleMap[shift.roleId] || null;
      const branch = locationMap[shift.branchId] || null;
      const department = departmentsMap[shift.departmentId] || null;

      return {
        id: shift.id,
        date: moment(shift.startTime).format("YYYY-MM-DD"),
        startTime: moment(shift.startTime).format("HH:mm"),
        endTime: moment(shift.endTime).format("HH:mm"),
        duration: moment.duration(moment(shift.endTime).diff(moment(shift.startTime))).asHours().toFixed(2),
        breakTime: shift.minutesBreak ? shift.minutesBreak : 0,
        userName: !shift.isOpen && user ? `${user.user_first_name} ${user.user_last_name}` : 'Open Shift',
        userEmail: !shift.isOpen && user ? user.user_email : '',
        roleName: role ? role.role_name : '',
        branchName: branch ? branch.branch_name : '',
        departmentName: department ? department.department_name : '',
        status: shift.status,
        isOpen: shift.isOpen ? 'Yes' : 'No',
        isPublished: shift.isPublished ? 'Yes' : 'No',
        notes: shift.notes || '',
      };
    });

    // Create Excel workbook and worksheet with enhanced styling
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Shifts Report', {
      pageSetup: {
        paperSize: 9,
        orientation: 'landscape',
        fitToPage: true,
        fitToWidth: 1,
        fitToHeight: 0
      }
    });

    // Company/Report Header Section
    const titleRow = worksheet.addRow(['SHIFT REPORT']);
    titleRow.font = { bold: true, size: 18, color: { argb: '2F4F4F' } };
    titleRow.alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.mergeCells('A1:O1');

    // Report metadata section
    worksheet.addRow([]);
    const metaStartRow = worksheet.rowCount + 1;

    worksheet.addRow(['Report Period:', `${moment(startDate).format('DD MMM YYYY')} - ${moment(endDate).format('DD MMM YYYY')}`]);
    worksheet.addRow(['Generated On:', moment().format('DD MMMM YYYY, h:mm A')]);
    worksheet.addRow(['Generated By:', `${req.user.user_first_name} ${req.user.user_last_name}`]);
    worksheet.addRow(['Total Shifts:', count]);

    // Style metadata section
    for (let i = metaStartRow; i <= worksheet.rowCount; i++) {
      const row = worksheet.getRow(i);
      row.getCell(1).font = { bold: true, color: { argb: '4A4A4A' } };
      row.getCell(2).font = { color: { argb: '666666' } };
    }

    worksheet.addRow([]); // Empty row for spacing

    // Define columns with enhanced headers
    const headerRow = worksheet.addRow([
      'Shift ID', 'Date', 'Start Time', 'End Time', 'Duration (hrs)',
      'Break (mins)', 'Employee Name', 'Email', 'Role', 'Branch',
      'Department', 'Status', 'Open Shift', 'Published', 'Notes'
    ]);

    // Set column widths
    worksheet.columns = [
      { key: 'id', width: 12 },
      { key: 'date', width: 14 },
      { key: 'startTime', width: 12 },
      { key: 'endTime', width: 12 },
      { key: 'duration', width: 14 },
      { key: 'breakTime', width: 12 },
      { key: 'userName', width: 22 },
      { key: 'userEmail', width: 28 },
      { key: 'roleName', width: 16 },
      { key: 'branchName', width: 16 },
      { key: 'departmentName', width: 18 },
      { key: 'status', width: 12 },
      { key: 'isOpen', width: 12 },
      { key: 'isPublished', width: 12 },
      { key: 'notes', width: 35 },
    ];

    // Enhanced header styling
    headerRow.eachCell((cell: any) => {
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.font = { bold: true, size: 11, color: { argb: 'FFFFFF' } };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '0070C0' } // Professional blue
      };
      cell.border = {
        top: { style: 'thin', color: { argb: '000000' } },
        left: { style: 'thin', color: { argb: '000000' } },
        bottom: { style: 'thin', color: { argb: '000000' } },
        right: { style: 'thin', color: { argb: '000000' } },
      };
    });
    headerRow.height = 25; // Increase header row height

    // Add data rows with enhanced styling
    processedShifts.forEach((shift: any, index: number) => {
      const dataRow = worksheet.addRow([
        shift.id, shift.date, shift.startTime, shift.endTime, shift.duration,
        shift.breakTime, shift.userName, shift.userEmail, shift.roleName,
        shift.branchName, shift.departmentName, shift.status, shift.isOpen,
        shift.isPublished, shift.notes
      ]);

      // Alternate row colors for better readability
      const isEvenRow = index % 2 === 0;
      const rowColor = isEvenRow ? 'F8F9FA' : 'FFFFFF';

      dataRow.eachCell((cell: any, colNumber: number) => {
        // Basic cell styling
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: rowColor }
        };
        cell.border = {
          top: { style: 'thin', color: { argb: 'E0E0E0' } },
          left: { style: 'thin', color: { argb: 'E0E0E0' } },
          bottom: { style: 'thin', color: { argb: 'E0E0E0' } },
          right: { style: 'thin', color: { argb: 'E0E0E0' } },
        };
        cell.alignment = { vertical: 'middle', wrapText: true };

        // Column-specific styling
        if (colNumber === 1) { // Shift ID
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
          cell.font = { bold: true, color: { argb: '0070C0' } };
        } else if (colNumber === 2) { // Date
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        } else if (colNumber === 3 || colNumber === 4) { // Start/End Time
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
          cell.font = { color: { argb: '2F4F4F' } };
        } else if (colNumber === 5) { // Duration
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
          cell.font = { bold: true, color: { argb: '228B22' } };
        } else if (colNumber === 7) { // Employee Name
          cell.font = { bold: true };
          if (shift.isOpen === 'Yes') {
            cell.font = { bold: true, color: { argb: 'FF6B35' } }; // Orange for open shifts
          }
        } else if (colNumber === 12) { // Status
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
          if (shift.status === 'active') {
            cell.font = { bold: true, color: { argb: '228B22' } }; // Green
          } else if (shift.status === 'pending') {
            cell.font = { bold: true, color: { argb: 'FF8C00' } }; // Orange
          }
        } else if (colNumber === 13 || colNumber === 14) { // Open Shift, Published
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
          if (cell.value === 'Yes') {
            cell.font = { bold: true, color: { argb: '228B22' } }; // Green
          } else {
            cell.font = { color: { argb: '999999' } }; // Gray
          }
        }
      });
      dataRow.height = 20; // Set consistent row height
    });

    // Add auto filter to data section
    const dataStartRow = worksheet.rowCount - processedShifts.length + 1;
    worksheet.autoFilter = {
      from: { row: dataStartRow - 1, column: 1 },
      to: { row: worksheet.rowCount, column: 15 }
    };

    // Add summary statistics section
    worksheet.addRow([]); // Empty row for spacing
    worksheet.addRow([]); // Empty row for spacing

    const summaryTitleRow = worksheet.addRow(['SUMMARY STATISTICS']);
    summaryTitleRow.font = { bold: true, size: 14, color: { argb: '2F4F4F' } };
    summaryTitleRow.alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.mergeCells(`A${summaryTitleRow.number}:E${summaryTitleRow.number}`);

    // Calculate statistics
    const totalHours = processedShifts.reduce((sum: number, shift: any) => sum + parseFloat(shift.duration), 0);
    const totalBreakTime = processedShifts.reduce((sum: number, shift: any) => sum + parseInt(shift.breakTime), 0);
    const openShiftsCount = processedShifts.filter((shift: any) => shift.isOpen === 'Yes').length;
    const publishedShiftsCount = processedShifts.filter((shift: any) => shift.isPublished === 'Yes').length;
    const activeShiftsCount = processedShifts.filter((shift: any) => shift.status === 'active').length;

    // Add summary data
    const summaryData = [
      ['Total Shifts:', count],
      ['Total Hours:', `${totalHours.toFixed(2)} hrs`],
      ['Total Break Time:', `${totalBreakTime} mins`],
      ['Open Shifts:', openShiftsCount],
      ['Published Shifts:', publishedShiftsCount],
      ['Active Shifts:', activeShiftsCount],
      ['Average Shift Duration:', `${(totalHours / processedShifts.length).toFixed(2)} hrs`]
    ];

    summaryData.forEach(([label, value]) => {
      const row = worksheet.addRow([label, value]);
      row.getCell(1).font = { bold: true, color: { argb: '4A4A4A' } };
      row.getCell(2).font = { color: { argb: '0070C0' }, bold: true };
      row.getCell(1).alignment = { horizontal: 'left', vertical: 'middle' };
      row.getCell(2).alignment = { horizontal: 'left', vertical: 'middle' };
    });

    // Freeze panes for better navigation
    worksheet.views = [
      { state: 'frozen', xSplit: 0, ySplit: dataStartRow - 1 }
    ];

    // Set print settings
    worksheet.pageSetup.margins = {
      left: 0.7, right: 0.7, top: 0.75, bottom: 0.75,
      header: 0.3, footer: 0.3
    };

    // Generate enhanced filename
    const dateRange = `${moment(startDate).format('DD-MMM-YYYY')}_to_${moment(endDate).format('DD-MMM-YYYY')}`;
    const timestamp = moment().format('YYYY-MM-DD_HH-mm');
    const filename = `Shifts_Report_${dateRange}_Generated_${timestamp}.xlsx`;

    // Set content type and disposition
    try {
      const buffer = await workbook.xlsx.writeBuffer();

      // Set response headers for Excel download
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', Buffer.byteLength(buffer).toString());

      // Send the buffer as a response
      return res.status(200).send(buffer);
    } catch (bufferError) {
      console.log("Error generating Excel buffer:", bufferError);
      return res.status(500).send({
        status: false,
        message: res.__("ERROR_GENERATING_EXCEL"),
        error: bufferError,
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

const changeGroupBy = async (req: Request, res: Response): Promise<any> => {
  try {
    const { groupBy } = req.body;

    if (groupBy != "") {
      await sequelize.query(`UPDATE nv_users SET rota_group_by = '${groupBy}' WHERE id = ${req.user.id}`);
    } else {
      await sequelize.query(`UPDATE nv_users SET rota_group_by = NULL WHERE id = ${req.user.id}`);
    }

    return res.status(200).send({
      status: true,
      message: res.__("SUCCESS_DATA_UPDATED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
}

const updateUserOrderList = async (req: Request, res: Response): Promise<any> => {
  try {
    const { sortOrder, userId } = req.body;

    // Get current user's order
    const currentUser = await getUser(userId)

    if (!currentUser) {
      return res.status(404).send({
        status: false,
        message: res.__("USER_NOT_FOUND")
      });
    }

    const currentOrder = currentUser?.list_order || null;

    // Begin transaction
    const transaction = await sequelize.transaction();

    try {
      if (currentOrder === null || currentOrder === sortOrder) {
        // If user doesn't have an order yet or same order, simply update
        await sequelize.query(
          `UPDATE nv_users SET list_order = ? WHERE id = ?`,
          {
            replacements: [sortOrder, userId],
            type: QueryTypes.UPDATE,
            transaction
          }
        );
      } else if (currentOrder < sortOrder) {
        // Moving down: Shift everyone up who is between current and new position
        await sequelize.query(
          `UPDATE nv_users
           SET list_order = list_order - 1
           WHERE list_order > ? AND list_order <= ? AND id != ?`,
          {
            replacements: [currentOrder, sortOrder, userId],
            type: QueryTypes.UPDATE,
            transaction
          }
        );

        // Set new position
        await sequelize.query(
          `UPDATE nv_users SET list_order = ? WHERE id = ?`,
          {
            replacements: [sortOrder, userId],
            type: QueryTypes.UPDATE,
            transaction
          }
        );
      } else {
        // Moving up: Shift everyone down who is between new and current position
        await sequelize.query(
          `UPDATE nv_users
           SET list_order = list_order + 1
           WHERE list_order >= ? AND list_order < ? AND id != ?`,
          {
            replacements: [sortOrder, currentOrder, userId],
            type: QueryTypes.UPDATE,
            transaction
          }
        );

        // Set new position
        await sequelize.query(
          `UPDATE nv_users SET list_order = ? WHERE id = ?`,
          {
            replacements: [sortOrder, userId],
            type: QueryTypes.UPDATE,
            transaction
          }
        );
      }

      // Commit transaction
      await transaction.commit();

      return res.status(200).send({
        status: true,
        message: res.__("SUCCESS_DATA_UPDATED")
      });
    } catch (error) {
      // Rollback transaction on error
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error
    });
  }
};

const importShifts = async (req: Request, res: Response): Promise<any> => {
  const mReq = req as MulterRequest;
  try {
    if (!mReq.file) {
      return res.status(400).send({ status: false, message: "No file uploaded" });
    }

    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(mReq.file.path);
    const worksheet = workbook.worksheets[0];

    // Parse rows to array of objects
    const rows: any[] = [];
    const headerRow = worksheet.getRow(1).values as string[];
    for (let i = 2; i <= worksheet.rowCount; i++) {
      const row = worksheet.getRow(i);
      if (!row || row.number === 1) continue;
      const obj: any = {};
      headerRow.forEach((header, idx) => {
        if (header) obj[header.toString().trim()] = row.getCell(idx).value;
      });
      rows.push(obj);
    }

    // Explicitly type results arrays
    const results: { successful: Array<{ row: number }>; failed: Array<{ row: number; error: string }> } = { successful: [], failed: [] };

    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];
      try {
        // 1. Get user by email
        const user = await getUserByEmail(row.emailId.text || row.emailId);
        if (!user) {
          results.failed.push({ row: i + 2, error: "User not found for email: " + row.emailId });
          continue;
        }

        // 2. Get branch by name, fallback to user's branch
        let branch = null;
        if (row["branch name"] && row["branch name"] !== "Select branch") {
          branch = await getBranchByName(row["branch name"]);
        }
        if (!branch && user.branch_id) {
          branch = { id: user.branch_id };
        }
        if (!branch) {
          results.failed.push({ row: i + 2, error: "Branch not found for: " + row["branch name"] });
          continue;
        }

        // 3. Department from user if not present
        const departmentId = user.department_id;

        // 4. Convert times
        const startTime = new Date(row["start time"]);
        const endTime = new Date(row["end time"]);
        if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
          results.failed.push({ row: i + 2, error: "Invalid start or end time" });
          continue;
        }

        // 5. Build shift object
        const shiftData = {
          userId: user.id,
          startTime,
          endTime,
          status: "active",
          minutesBreak: 0,
          branchId: branch.id,
          departmentId: departmentId,
          roleId: user.user_role_id,
          isOpen: false,
          isPublished: true,
          isSwap: false,
          acknowledged: true,
          notes: "",
        };

        // 6. Validate
        const tempReq = { ...req, body: shiftData };
        const validateShift = await commonShiftValidator(tempReq);
        if (validateShift) {
          results.failed.push({ row: i + 2, error: validateShift.message });
          continue;
        }

        // 7. Create shift
        const createdShift = await Shift.create({
          ...shiftData,
          createdBy: req.user.id,
          updatedBy: req.user.id,
          organization_id: req.user.organization_id,
        });
        // Respond with the created shift for debugging/confirmation
        console.log("Shift created:", createdShift.toJSON ? createdShift.toJSON() : createdShift);
        results.successful.push({ row: i + 2 });
      } catch (err: any) {
        results.failed.push({ row: i + 2, error: err.message });
      }
    }

    // Delete file after processing
    fs.unlinkSync(mReq.file.path);

    return res.status(200).send({
      status: true,
      message: "Import completed",
      data: results,
    });
  } catch (error: any) {
    // Delete file if error
    const mReq = req as MulterRequest;
    if (mReq.file && mReq.file.path && fs.existsSync(mReq.file.path)) {
      fs.unlinkSync(mReq.file.path);
    }
    return res.status(500).send({
      status: false,
      message: "Something went wrong",
      error: error.message || error,
    });
  }
};

// Helper to get all branches for the current company
const getAllBranches = async (organization_id: string) => {
  const branches = await sequelize.query(
    `SELECT branch_name FROM nv_branches WHERE organization_id = :organization_id`,
    { replacements: { organization_id }, type: QueryTypes.SELECT, raw: true }
  );
  return branches.map((b: any) => b.branch_name);
};

// Function to generate Excel template as buffer
async function generateExcelTemplate(branchNames: string[]) {
  const workbook = new ExcelJS.Workbook();
  const sheet = workbook.addWorksheet('Template');

  // Define headers
  sheet.columns = [
    { header: 'emailId', key: 'emailId', width: 30 },
    { header: 'start time', key: 'startTime', width: 30 },
    { header: 'end time', key: 'endTime', width: 30 },
    { header: 'branch name', key: 'branchName', width: 30 },
  ];

  const placeholders = {
    emailId: '<EMAIL>',
    startTime: new Date('2025-07-23T09:00:00.000Z'),
    endTime: new Date('2025-07-23T17:00:00.000Z'),
    branchName: 'Select branch',
  };

  for (let i = 0; i < 10; i++) {
    const rowNumber = i + 2;

    sheet.getCell(`A${rowNumber}`).value = placeholders.emailId;

    const startCell = sheet.getCell(`B${rowNumber}`);
    startCell.value = placeholders.startTime;
    startCell.numFmt = 'yyyy-mm-ddThh:mm:ss';

    const endCell = sheet.getCell(`C${rowNumber}`);
    endCell.value = placeholders.endTime;
    endCell.numFmt = 'yyyy-mm-ddThh:mm:ss';

    const branchCell = sheet.getCell(`D${rowNumber}`);
    branchCell.value = placeholders.branchName;
    branchCell.dataValidation = {
      type: 'list',
      allowBlank: false,
      formulae: [`"${branchNames.join(',')}"`],
      showErrorMessage: true,
      errorStyle: 'warning',
      errorTitle: 'Invalid input',
      error: `Select from: ${branchNames.join(', ')}`,
    };
  }

  // Return as buffer for download
  return await workbook.xlsx.writeBuffer();
}

// GET API handler
const getShiftImportTemplate = async (req: Request, res: Response): Promise<any> => {
  try {
    const organization_id = req.user.organization_id;
    const branchNames = await getAllBranches(organization_id);
    if (!branchNames.length) {
      return res.status(404).send({ status: false, message: 'No branches found for this company.' });
    }
    const buffer = await generateExcelTemplate(branchNames);
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename="shift_import_template.xlsx"');
    res.setHeader('Content-Length', buffer.byteLength.toString());
    return res.status(200).send(buffer);
  } catch (error: any) {
    return res.status(500).send({
      status: false,
      message: 'Failed to generate template',
      error: error.message || error,
    });
  }
};

const getBestShiftsForUsers = async (req: Request, res: Response): Promise<any> => {
  try {
    // Ensure all filters are strings or undefined, never ParsedQs
    const getString = (v: any): string | undefined => {
      if (typeof v === 'string') return v;
      if (Array.isArray(v) && typeof v[0] === 'string') return v[0];
      return undefined;
    };
    const userId = getString(req.query.userId);
    const roleId = getString(req.query.roleId);
    const departmentId = getString(req.query.departmentId);
    const branchId = getString(req.query.branchId);
    const startTime = getString(req.query.startTime);
    const endTime = getString(req.query.endTime);
    const organization_id = req.user.organization_id;
    const filters: { userId?: string, roleId?: string, departmentId?: string, branchId?: string, startTime?: string, endTime?: string } = { userId, roleId, departmentId, branchId, startTime, endTime };
    const results = await getBestShifts(organization_id, filters);
    // Ensure results is an array, not a Response
    return res.status(200).send({
      status: true,
      message: 'Best shifts fetched successfully',
      data: Array.isArray(results) ? results : [],
    });
  } catch (error: any) {
    return res.status(500).send({
      status: false,
      message: 'Failed to fetch best shifts',
      error: error.message || error,
    });
  }
};

const getRotaReports = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      from,
      to,
      branchId,
      departmentId,
      search,
      download
    }: any = req.query;

    // Check permissions
    const checkPermission = await permittedForAdmin(req.user?.id, [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.HR,
    ]);

    // Set date range - if not provided, use current month
    let startDate: string;
    let endDate: string;

    if (from && to) {
      startDate = moment(from).startOf("day").format("YYYY-MM-DD HH:mm:ss");
      endDate = moment(to).endOf("day").format("YYYY-MM-DD HH:mm:ss");
    } else {
      // Use current month if dates not provided
      startDate = moment().startOf("month").format("YYYY-MM-DD HH:mm:ss");
      endDate = moment().endOf("month").format("YYYY-MM-DD HH:mm:ss");
    }


    // Build SQL query conditions
    let userCondition = "";
    let userBranchCondition = "";
    let userDepartmentCondition = "";
    let shiftBranchCondition = "";
    let shiftDepartmentCondition = "";
    let searchCondition = "";

    // For non-admin users, only show their own data
    if (!checkPermission) {
      userCondition = `AND u.id = ${req?.user?.id}`;
    }
    // For admin users, show all users in the organization (no additional user condition needed)

    // Apply branch/department filters - ultra-simple approach
    if (branchId) {
      userBranchCondition = `AND FIND_IN_SET(u.branch_id, '${branchId}')`;
      shiftBranchCondition = `AND FIND_IN_SET(s.branchId, '${branchId}')`;
    }

    if (departmentId) {
      userDepartmentCondition = `AND FIND_IN_SET(u.department_id, '${departmentId}')`;
      shiftDepartmentCondition = `AND FIND_IN_SET(s.departmentId, '${departmentId}')`;
    }

    if (search) {
      searchCondition = `AND (u.user_first_name LIKE '%${search}%' OR u.user_last_name LIKE '%${search}%' OR u.user_email LIKE '%${search}%')`;
    }

    // Optimized SQL query to get all data in one go
    const reportQuery = `
      SELECT
        u.id as userId,
        u.employment_number as emp_id,
        u.user_first_name,
        u.user_last_name,
        u.user_email,
        ub.branch_name as user_branch,
        ud.department_name as user_department,
        COUNT(CASE WHEN s.id IS NOT NULL THEN 1 END) as total_shifts,
        ROUND(COALESCE(SUM(
          TIMESTAMPDIFF(MINUTE, s.startTime, s.endTime) - COALESCE(s.minutesBreak, 0)
        ) / 60, 0), 2) as total_hours,
        ROUND(COALESCE(SUM(COALESCE(s.minutesBreak, 0)) / 60, 0), 2) as total_break_hours
      FROM nv_users u
      LEFT JOIN shifts s ON u.id = s.userId
        AND s.status != 'deleted'
        AND s.organization_id = '${req.user.organization_id}'
        AND s.startTime >= '${startDate}'
        AND s.endTime <= '${endDate}'
        ${shiftBranchCondition}
        ${shiftDepartmentCondition}
      LEFT JOIN nv_branches ub ON u.branch_id = ub.id
      LEFT JOIN nv_departments ud ON u.department_id = ud.id
      WHERE u.organization_id = '${req.user.organization_id}'
        AND u.user_status NOT IN ('cancelled', 'deleted')
        AND (u.web_user_active_role_id IS NULL OR u.web_user_active_role_id != 1)
        ${userCondition}
        ${userBranchCondition}
        ${userDepartmentCondition}
        ${searchCondition}
      GROUP BY u.id, u.employment_number, u.user_first_name, u.user_last_name, u.user_email, ub.branch_name, ud.department_name
      ORDER BY u.user_first_name, u.user_last_name`;

    const reportResults = await sequelize.query(reportQuery, {
      type: QueryTypes.SELECT,
      raw: true,
    });

    if (!reportResults.length) {
      return res.status(200).send({
        status: true,
        message: res.__("SUCCESS_DATA_FETCHED"),
        count: 0,
        data: [],
        period: {
          from: moment(startDate, "YYYY-MM-DD HH:mm:ss").format("YYYY-MM-DD"),
          to: moment(endDate, "YYYY-MM-DD HH:mm:ss").format("YYYY-MM-DD"),
        },
      });
    }

    // Process the results
    const reportData = reportResults.map((row: any) => {
      return {
        userId: row.userId,
        employment_number: row.emp_id,
        user_full_name: `${row.user_first_name} ${row.user_last_name}`,
        branch: row.user_branch,
        department: row.user_department,
        total_shifts: parseInt(row.total_shifts),
        total_hours: parseFloat(row.total_hours || 0),
        total_break_hours: parseFloat(row.total_break_hours || 0),
      };
    });

    // Handle download functionality
    if (download) {
      if (download === 'excel') {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Rota Reports');

        // Title row
        const titleRow = worksheet.addRow(['Rota Reports']);
        worksheet.mergeCells('A1:G1');
        titleRow.eachCell((cell: any) => {
          cell.font = { bold: true, size: 14 };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });

        // Get branch and department names
        let branchName = 'all';
        let departmentName = 'all';

        if (branchId) {
          const branchQuery = `SELECT branch_name FROM nv_branches WHERE id = ? AND organization_id = ?`;
          const branchResult = await sequelize.query(branchQuery, {
            replacements: [branchId, req.user.organization_id],
            type: QueryTypes.SELECT,
            raw: true,
          });
          branchName = branchResult.length > 0 ? branchResult[0].branch_name : 'all';
        }

        if (departmentId) {
          const departmentQuery = `SELECT department_name FROM nv_departments WHERE id = ? AND organization_id = ?`;
          const departmentResult = await sequelize.query(departmentQuery, {
            replacements: [departmentId, req.user.organization_id],
            type: QueryTypes.SELECT,
            raw: true,
          });
          departmentName = departmentResult.length > 0 ? departmentResult[0].department_name : 'all';
        }

        // Filter information
        const filterRows = [
          worksheet.addRow(['Period:', `${moment(startDate, "YYYY-MM-DD HH:mm:ss").format("YYYY-MM-DD")} to ${moment(endDate, "YYYY-MM-DD HH:mm:ss").format("YYYY-MM-DD")}`]),
          worksheet.addRow(['Branch:', branchName]),
          worksheet.addRow(['Department:', departmentName]),
          worksheet.addRow(['Report Date:', moment().format('DD MMMM YYYY, h:mm A')])
        ];
        worksheet.addRow([]); // Empty row for spacing

        // Set fixed widths for the first two columns to prevent truncation
        worksheet.getColumn(1).width = 30; // Label column
        worksheet.getColumn(2).width = 40; // Value column

        // Style filter info
        filterRows.forEach((row) => {
          row.eachCell((cell: any, colNumber: number) => {
            if (colNumber === 1) {
              cell.font = { bold: true };
            }
            cell.alignment = { vertical: 'middle', wrapText: true };
          });
        });

        // Headers
        const headers = [
          'Employment Number',
          'Full Name',
          'Branch',
          'Department',
          'Total Shifts',
          'Total Hours',
          'Total Break Hours'
        ];
        const headerRow = worksheet.addRow(headers);

        // Style headers
        headerRow.eachCell((cell: any, colNumber: number) => {
          cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '0070C0' } };
          cell.font = { bold: true, color: { argb: 'FFFFFF' } };
          cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
          cell.border = {
            top: { style: 'thin', color: { argb: '000000' } },
            left: { style: 'thin', color: { argb: '000000' } },
            bottom: { style: 'thin', color: { argb: '000000' } },
            right: { style: 'thin', color: { argb: '000000' } },
          };

          // Set column widths
          const column = worksheet.getColumn(colNumber);
          switch (colNumber) {
            case 1: // Employment Number
              column.width = 20;
              break;
            case 2: // Full Name
              column.width = 25;
              break;
            case 3: // Branch
              column.width = 20;
              break;
            case 4: // Department
              column.width = 20;
              break;
            case 5: // Total Shifts
              column.width = 15;
              break;
            case 6: // Total Hours
              column.width = 15;
              break;
            case 7: // Total Break Hours
              column.width = 18;
              break;
          }
        });

        // Add data rows
        reportData.forEach((row: any) => {
          const rowData = [
            row.employment_number || '',
            row.user_full_name || '',
            row.branch || '',
            row.department || '',
            row.total_shifts || 0,
            row.total_hours || 0,
            row.total_break_hours || 0
          ];
          worksheet.addRow(rowData);
        });

        // Style data rows
        worksheet.eachRow((row: any, rowNumber: number) => {
          if (rowNumber > 6) { // Skip title and header rows
            row.eachCell((cell: any, colNumber: number) => {
              cell.alignment = { horizontal: 'center', vertical: 'middle' };
              cell.border = {
                top: { style: 'thin', color: { argb: '000000' } },
                left: { style: 'thin', color: { argb: '000000' } },
                bottom: { style: 'thin', color: { argb: '000000' } },
                right: { style: 'thin', color: { argb: '000000' } },
              };
            });
          }
        });

        // Generate and send file
        const buffer = await workbook.xlsx.writeBuffer();
        res.setHeader('Content-Disposition', 'attachment; filename=rota_reports.xlsx');
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.send(buffer);
        return;
      } else if (download === 'csv') {
        // Transform data for CSV
        const headers = [
          'Employment Number',
          'Full Name',
          'Branch',
          'Department',
          'Total Shifts',
          'Total Hours',
          'Total Break Hours'
        ];

        const csvRows = [headers.join(',')];

        reportData.forEach((row: any) => {
          const rowData = [
            `"${row.employment_number || ''}"`,
            `"${row.user_full_name || ''}"`,
            `"${row.branch || ''}"`,
            `"${row.department || ''}"`,
            row.total_shifts || 0,
            row.total_hours || 0,
            row.total_break_hours || 0
          ];
          csvRows.push(rowData.join(','));
        });

        const buffer = Buffer.from(csvRows.join('\n'), 'utf-8');
        res.setHeader('Content-Type', 'text/csv; charset=utf-8');
        res.setHeader('Content-Disposition', 'attachment; filename="rota_reports.csv"');
        res.setHeader('Content-Length', buffer.length.toString());
        res.end(buffer);
        return;

      } else if (download === 'pdf') {
        const [contentHtml, footerHtml] = await Promise.all([
          new Promise((resolve, reject) => {
            readHTMLFile(
              path.join(__dirname, '../../src/email_templates/user_rota_reports.html'),
              (err: any, html: any) => {
                if (err) reject(err);
                else resolve(html);
              }
            );
          }),
          new Promise((resolve, reject) => {
            readHTMLFile(
              path.join(__dirname, '../../src/email_templates/footer.html'),
              (err: any, html: any) => {
                if (err) reject(err);
                else resolve(html);
              }
            );
          }),
        ]);

        const combinedTemplate = `${contentHtml}${footerHtml}`;
        try {
          const compiledTemplate = handlebars.compile(combinedTemplate);

          const findGeneralSetting: any = await getGeneralSettingObj(req.user.organization_id);

          // Get branch and department names for PDF
          let branchNameForPdf = 'all';
          let departmentNameForPdf = 'all';

          if (branchId) {
            const branchQuery = `SELECT branch_name FROM nv_branches WHERE id = ? AND organization_id = ?`;
            const branchResult = await sequelize.query(branchQuery, {
              replacements: [branchId, req.user.organization_id],
              type: QueryTypes.SELECT,
              raw: true,
            });
            branchNameForPdf = branchResult.length > 0 ? branchResult[0].branch_name : 'all';
          }

          if (departmentId) {
            const departmentQuery = `SELECT department_name FROM nv_departments WHERE id = ? AND organization_id = ?`;
            const departmentResult = await sequelize.query(departmentQuery, {
              replacements: [departmentId, req.user.organization_id],
              type: QueryTypes.SELECT,
              raw: true,
            });
            departmentNameForPdf = departmentResult.length > 0 ? departmentResult[0].department_name : 'all';
          }
          console.log(reportData);
          const dataObj: any = {
            rota_data: reportData,
            period_from: moment(startDate, "YYYY-MM-DD HH:mm:ss").format("YYYY-MM-DD"),
            period_to: moment(endDate, "YYYY-MM-DD HH:mm:ss").format("YYYY-MM-DD"),
            branch_filter: branchNameForPdf,
            department_filter: departmentNameForPdf,
            current_date: moment().format('DD MMMM YYYY, h:mm A'),
            NAMASTE_LOGO: findGeneralSetting.brand_logo_link || 'https://via.placeholder.com/100x50?text=Logo',
            GENERATED_BY_USER: req.user.user_first_name + " " + req.user.user_last_name || 'System',
            CONFIDENTIALITY_STATEMENT: 'Internal Use Only',
            MICROFFICE_LOGO_URL: global.config.API_UPLOAD_URL + "email_logo/logo.png"
          };
          const htmlToSend = compiledTemplate(dataObj);
          console.log("HTML template:", htmlToSend);
          console.log("HTML template length:", htmlToSend.length);

          if (!htmlToSend || htmlToSend.length === 0) {
            throw new Error('Generated HTML template is empty');
          }

          const pdfBuffer: any = await generateFile(download, htmlToSend);
          console.log("PDF Buffer type:", typeof pdfBuffer);
          console.log("PDF Buffer length:", pdfBuffer?.length);
          console.log("PDF Buffer length:", pdfBuffer.toString());
          res.set({
            'Content-Type': 'application/pdf',
            'Content-Disposition': 'attachment; filename=rota_reports.pdf',
            'Content-Length': pdfBuffer.length.toString()
          });

          console.log("PDF generated and sent successfully! Size:", pdfBuffer.length);
          console.log("PDF generated and sent successfully! Size:", pdfBuffer.length);
          res.send(pdfBuffer);
          return;
        } catch (e) {
          console.log("error", e);
          return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
          });
        }
      }
    }

    return res.status(200).send({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      count: reportData.length,
      data: reportData,
      period: {
        from: moment(startDate, "YYYY-MM-DD HH:mm:ss").format("YYYY-MM-DD"),
        to: moment(endDate, "YYYY-MM-DD HH:mm:ss").format("YYYY-MM-DD"),
      },
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export {
  addShift,
  addShiftsBatch,
  getShifts,
  updateShift,
  checkShiftAvailability,
  setAsOpenShift,
  publishUnpublishShift,
  swapShift,
  swapActions,
  dropShift,
  getShiftHistory,
  copyShiftsRange,
  checkCopyShiftTimeConflict,
  deleteShift,
  clearWeekShifts,
  dropAction,
  claimOpenShift,
  exportShiftsToExcel,
  changeGroupBy,
  updateUserOrderList,
  getRotaReports,
  importShifts,
  getShiftImportTemplate,
  getBestShiftsForUsers,
};


