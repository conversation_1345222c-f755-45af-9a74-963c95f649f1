import { Request, Response } from "express";
import { validateModulePermission, getPlatformFromRequest } from "../helper/common";
import { ROLE_PERMISSIONS } from "../helper/constant";
import {
  main as runMigration,
  createRoleMapping,
  getUserRoleInfo,
  analyzeShifts,
  validateMigration
} from "../scripts/migrateShiftRolesToMoRoles";

/**
 * Get migration analysis without making any changes
 * @param req - Express request object
 * @param res - Express response object
 * @returns Response with migration analysis
 */
const getMigrationAnalysis = async (req: Request, res: Response): Promise<any> => {
  try {
    // Check permission - only super admin should access migration
    const checkPermission = await validateModulePermission(
      req.user, 
      req.user.organization_id, 
      "rotas", 
      ROLE_PERMISSIONS.DELETE, // Using highest permission level
      getPlatformFromRequest(req)
    );
    
    if (!checkPermission) {
      return res.status(403).json({
        status: false,
        message: "Permission denied. Only super administrators can access migration features."
      });
    }

    console.log(`🔍 Migration analysis requested by user ${req.user.id}`);

    // Get all necessary data for analysis
    const { roleMapping, mappingResults } = await createRoleMapping();
    const userRoles = await getUserRoleInfo();
    const shifts = await analyzeShifts();

    // Analysis counters
    const stats = {
      totalShifts: shifts.length,
      shiftsToMigrate: 0,
      shiftsAlreadyCorrect: 0,
      shiftsWithIssues: 0,
      organizationsAffected: new Set<string>(),
      rolesAffected: new Set<number>(),
      migrationReasons: {
        role_mapping: 0,
        user_role_validation: 0,
        no_mapping_found: 0,
        user_no_role_id: 0
      }
    };

    const issues: any[] = [];
    const migrationPlan: any[] = [];

    // Import the determination function
    const { determineCorrectMoRoleId } = await import("../scripts/migrateShiftRolesToMoRoles");

    for (const shift of shifts) {
      const newMoRoleId = determineCorrectMoRoleId(shift, roleMapping, userRoles);
      
      stats.organizationsAffected.add(shift.organization_id);
      stats.rolesAffected.add(shift.current_nv_role_id);
      
      if (!newMoRoleId) {
        stats.shiftsWithIssues++;
        stats.migrationReasons.no_mapping_found++;
        issues.push({
          shiftId: shift.shift_id,
          issue: 'No mo_role mapping found',
          details: `nv_role_id: ${shift.current_nv_role_id}, org: ${shift.organization_id}`
        });
      } else if (newMoRoleId === shift.current_nv_role_id) {
        stats.shiftsAlreadyCorrect++;
      } else {
        stats.shiftsToMigrate++;
        
        // Determine migration reason
        let reason = 'role_mapping';
        if (shift.shift_user_id) {
          const userHasRole = userRoles.some(ur => 
            ur.user_id === shift.shift_user_id && 
            ur.nv_role_id === shift.current_nv_role_id &&
            ur.organization_id === shift.organization_id
          );
          
          if (!userHasRole && shift.user_mo_role_id) {
            reason = 'user_role_validation';
          } else if (!userHasRole && !shift.user_mo_role_id) {
            reason = 'user_no_role_id';
            stats.migrationReasons.user_no_role_id++;
          }
        }
        
        stats.migrationReasons[reason as keyof typeof stats.migrationReasons]++;
        
        migrationPlan.push({
          shiftId: shift.shift_id,
          organizationId: shift.organization_id,
          currentRoleId: shift.current_nv_role_id,
          newRoleId: newMoRoleId,
          currentRoleName: shift.current_role_name,
          userId: shift.shift_user_id,
          reason: reason
        });
      }
    }

    // Convert Sets to arrays for JSON response
    const finalStats = {
      ...stats,
      organizationsAffected: Array.from(stats.organizationsAffected),
      rolesAffected: Array.from(stats.rolesAffected)
    };

    return res.status(200).json({
      status: true,
      message: "Migration analysis completed successfully",
      data: {
        analysis: finalStats,
        issues: issues.slice(0, 50), // Limit issues for response size
        sampleMigrationPlan: migrationPlan.slice(0, 20), // Sample of migration plan
        roleMappings: mappingResults.slice(0, 50), // Sample of role mappings
        readyForMigration: stats.shiftsWithIssues === 0,
        totalIssues: issues.length,
        totalMigrationPlan: migrationPlan.length
      }
    });

  } catch (error) {
    console.error("Migration analysis error:", error);
    return res.status(500).json({
      status: false,
      message: "Migration analysis failed. Please check server logs for details.",
      error: (error as Error).message,
    });
  }
};

/**
 * Execute the migration with safety checks
 * @param req - Express request object
 * @param res - Express response object
 * @returns Response with migration results
 */
const executeMigration = async (req: Request, res: Response): Promise<any> => {
  try {
    // Check permission - only super admin should execute migration
    const checkPermission = await validateModulePermission(
      req.user, 
      req.user.organization_id, 
      "rotas", 
      ROLE_PERMISSIONS.DELETE, // Using highest permission level
      getPlatformFromRequest(req)
    );
    
    if (!checkPermission) {
      return res.status(403).json({
        status: false,
        message: "Permission denied. Only super administrators can execute migration."
      });
    }

    console.log(`🚀 Migration execution requested by user ${req.user.id}`);

    // Pre-migration safety check
    const { roleMapping } = await createRoleMapping();
    const userRoles = await getUserRoleInfo();
    const shifts = await analyzeShifts();

    if (shifts.length === 0) {
      return res.status(400).json({
        status: false,
        message: "No shifts found that require migration."
      });
    }

    // Check for critical issues
    let hasIssues = false;
    const issues: string[] = [];

    if (Object.keys(roleMapping).length === 0) {
      hasIssues = true;
      issues.push("No role mappings found between nv_roles and mo_roles");
    }

    if (userRoles.length === 0) {
      hasIssues = true;
      issues.push("No user role assignments found");
    }

    if (hasIssues) {
      return res.status(400).json({
        status: false,
        message: "Migration safety check failed. Please resolve the issues before proceeding.",
        issues: issues
      });
    }

    // Execute migration
    console.log(`📊 Starting migration for ${shifts.length} shifts...`);
    
    // Run the migration
    await runMigration();

    // Post-migration validation
    await validateMigration();

    console.log(`✅ Migration completed successfully by user ${req.user.id}`);

    return res.status(200).json({
      status: true,
      message: "Migration completed successfully. Please verify the results using the validation endpoint.",
      data: {
        totalShiftsProcessed: shifts.length,
        executedBy: req.user.id,
        executedAt: new Date().toISOString(),
        message: "Migration completed successfully. Please verify the results."
      }
    });

  } catch (error) {
    console.error("Migration execution error:", error);
    return res.status(500).json({
      status: false,
      message: "Migration execution failed. Please check server logs and contact support if the issue persists.",
      error: (error as Error).message,
    });
  }
};

/**
 * Validate migration results
 * @param req - Express request object
 * @param res - Express response object
 * @returns Response with validation results
 */
const validateMigrationResults = async (req: Request, res: Response): Promise<any> => {
  try {
    // Check permission
    const checkPermission = await validateModulePermission(
      req.user, 
      req.user.organization_id, 
      "rotas", 
      ROLE_PERMISSIONS.VIEW,
      getPlatformFromRequest(req)
    );
    
    if (!checkPermission) {
      return res.status(403).json({
        status: false,
        message: "Permission denied. Insufficient privileges to validate migration results."
      });
    }

    console.log(`🔍 Migration validation requested by user ${req.user.id}`);

    // Run validation
    await validateMigration();

    return res.status(200).json({
      status: true,
      message: "Migration validation completed successfully. Check server logs for detailed results.",
      data: {
        validatedBy: req.user.id,
        validatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error("Migration validation error:", error);
    return res.status(500).json({
      status: false,
      message: "Migration validation failed. Please check server logs for details.",
      error: (error as Error).message,
    });
  }
};

export {
  getMigrationAnalysis,
  executeMigration,
  validateMigrationResults
};
