worker_processes auto;

events {
    worker_connections  1024;
}

http {
    # HTTP server block
    server {
        listen 81;
        server_name localhost;  # Replace with your domain if needed

        # Route for the API endpoint (server)
        location /auth-api {
            rewrite ^/auth-api(/.*)?$ $1 break;
            proxy_pass http://auth-ms:9023;  # Proxy traffic to the server service
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /auth/ {
            proxy_pass http://keycloak:8080/auth/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";  
        }

        # Route for the API endpoint (server)
        location /backend-api {
            rewrite ^/backend-api(/.*)?$ $1 break;
            proxy_pass http://backend-ms:8029;  # Proxy traffic to the server service
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # HTTPS server block
    server {
            listen 443 ssl;                # Ensure Nginx listens for HTTPS on port 443
            server_name localhost;         # Set the server_name to localhost or your domain

            ssl_certificate      /etc/nginx/nginx-selfsigned.crt;   # Path inside the container
            ssl_certificate_key  /etc/nginx/nginx-selfsigned.key;

            # Route for the API endpoint (server)
            location /auth-api {
                rewrite ^/auth-api(/.*)?$ $1 break;
                proxy_pass http://auth-ms:9023;  # Proxy traffic to the server service
                proxy_http_version 1.1;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            location /auth/ {
                proxy_pass http://keycloak:8080/auth/;  # Proxy traffic to the server service
                proxy_http_version 1.1;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Host $host;
                proxy_set_header X-Forwarded-Port $server_port;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
            }

            # Route for the API endpoint (server)
            location /backend-api {
                rewrite ^/backend-api(/.*)?$ $1 break;
                proxy_pass http://backend-ms:8029;  # Proxy traffic to the server service
                proxy_http_version 1.1;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
     }
}
