import express from "express";
import {
  getMigrationAnalysis,
  executeMigration,
  validateMigrationResults
} from "../../controller/migration.controller";


const router = express.Router();

/**
 * @swagger
 * /api/private/migration/analysis:
 *   get:
 *     summary: Get migration analysis
 *     description: Analyze shift role migration without making any changes (dry run)
 *     tags: [Migration]
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Migration analysis completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     analysis:
 *                       type: object
 *                       properties:
 *                         totalShifts:
 *                           type: integer
 *                         shiftsToMigrate:
 *                           type: integer
 *                         shiftsAlreadyCorrect:
 *                           type: integer
 *                         shiftsWithIssues:
 *                           type: integer
 *                         organizationsAffected:
 *                           type: array
 *                           items:
 *                             type: string
 *                         rolesAffected:
 *                           type: array
 *                           items:
 *                             type: integer
 *                         migrationReasons:
 *                           type: object
 *                     issues:
 *                       type: array
 *                       items:
 *                         type: object
 *                     sampleMigrationPlan:
 *                       type: array
 *                       items:
 *                         type: object
 *                     readyForMigration:
 *                       type: boolean
 *       403:
 *         description: Permission denied
 *       500:
 *         description: Internal server error
 */
router.get("/analysis", getMigrationAnalysis);

/**
 * @swagger
 * /api/private/migration/execute:
 *   post:
 *     summary: Execute shift role migration
 *     description: Execute the migration from nv_roles to mo_roles with safety checks
 *     tags: [Migration]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - confirmMigration
 *             properties:
 *               confirmMigration:
 *                 type: string
 *                 enum: [CONFIRM_MIGRATION]
 *                 description: Must be exactly "CONFIRM_MIGRATION" to proceed
 *           example:
 *             confirmMigration: "CONFIRM_MIGRATION"
 *     responses:
 *       200:
 *         description: Migration completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalShiftsProcessed:
 *                       type: integer
 *                     executedBy:
 *                       type: integer
 *                     executedAt:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: Bad request - confirmation required or safety check failed
 *       403:
 *         description: Permission denied
 *       500:
 *         description: Internal server error
 */
router.post("/execute", executeMigration);

/**
 * @swagger
 * /api/private/migration/validate:
 *   get:
 *     summary: Validate migration results
 *     description: Validate that the migration completed successfully and all shifts reference valid mo_roles
 *     tags: [Migration]
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Migration validation completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     validatedBy:
 *                       type: integer
 *                     validatedAt:
 *                       type: string
 *                       format: date-time
 *       403:
 *         description: Permission denied
 *       500:
 *         description: Internal server error
 */
router.get("/validate", validateMigrationResults);

export default router;
