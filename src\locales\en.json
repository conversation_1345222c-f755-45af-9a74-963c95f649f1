{"SOMETHING_WENT_WRONG": "something went wrong", "ERROR_INVALID_TOKEN": "invalid token", "ERROR_TOKEN_REQUIRED": "token required", "ERROR_USER_NOT_FOUND_IN_KEYCLOAK": "user not found in the keycloak storage", "SUCCESS_DATA_FETCHED": "successfully data fetched", "SUCCESS_DATA_UPDATED": "successfully data updated", "ERROR_USER_NOT_FOUND": "user not found", "SHIFT_ADDED": "Shift created successfully", "SHIFT_NOT_FOUND": "Shift not found", "SHIFT_SET_TO_OPEN": "Shift set as open successfully", "SHIFT_DROPPED": "Shift drop successfully", "SHIFT_AVAILABLE": "Shift available", "SHIFT_PUBLISHED": "Shift published successfully", "SHIFT_UNPUBLISHED": "Shift unpublished successfully", "ROLE_NOT_FOUND": "Role not found", "SWAP_EXISTS": "Swap request already exists", "SWAP_CREATED": "Swap request created successfully", "SWAP_NOT_FOUND": "Swap request not found", "SWAP_ACCEPTED": "Swap request accepted successfully", "SWAP_REJECTED": "Swap request rejected successfully", "DAY_OFF_EXISTS": "Day off already exists for the user", "DAY_OFF_CREATED": "Day off created successfully", "DAY_OFF_NOT_FOUND": "Day off not found", "DAY_OFF_DELETED": "Day off deleted successfully", "START_TIME_MUST_BE_BEFORE_END_TIME": "Start time must be before end time", "OVERLAPPING_AVAILABILITY_EXISTS": "Overlapping availability exists", "AVAILABILITY_CREATED": "Availability created successfully", "AVAILABILITY_NOT_FOUND": "Availability not found", "AVAILABILITY_DELETED": "Availability deleted successfully", "AVAILABILITY_UPDATED": "Availability updated successfully", "START_TIME_MUST_BE_AFTER_TODAY": "Start time must be future date", "END_TIME_MUST_BE_AFTER_TODAY": "End time must be future date", "PERMISSION_DENIED": "You don't have permission.", "SHIFTS_TIME_CONFLICT": "shift time conflict detected", "SHIFTS_NO_CONFLICT": "shift time no conflict", "INVALID_DATE_RANGE": "Invalid date range", "SHIFTS_COPIED_SUCCESSFULLY": "Shifts copied successfully", "SHIFT_DELETED": "Shift deleted successfully", "DROP_SHIFT_APPROVED": "Drop shift request approved", "DROP_SHIFT_REJECTED": "Drop shift request denied", "SHIFT_CLAIMED": "Shift claimed successfully", "DROP_ALREADY_REQUESTED": "Drop shift request already requested", "DROP_ALREADY_DENIED": "You have previously had a shift drop denied for this shift", "START_DATE_AND_END_DATE_REQUIRED": "Start date and end date are required", "FAIL_TOKEN_EXPIRED": "Session is expired. Please log in again.", "ERR_TOKEN_NOT_FOUND": "Token not found. Please log in again."}