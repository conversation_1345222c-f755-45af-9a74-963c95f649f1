/**
 * Migration Script: Migrate Shift roleId from nv_roles to mo_roles
 * 
 * This script performs the following operations:
 * 1. Maps roles from nv_roles to mo_roles based on role_name and organization_id
 * 2. Updates shifts.roleId to use mo_roles.id instead of nv_roles.id
 * 3. Handles user role validation using user_role table and user_role_id
 * 4. Ensures data consistency across the migration
 * 
 * Usage: npx ts-node src/scripts/migrateShiftRolesToMoRoles.ts
 */

import { QueryTypes } from 'sequelize';
import { sequelize } from '../models';
import * as dotenv from 'dotenv';

dotenv.config();

// TypeScript interfaces
interface RoleMapping {
  nv_role_id: number;
  role_name: string;
  mo_role_id: number;
  organization_id: string;
}

interface RoleMappingResult {
  roleMapping: { [nvRoleId: number]: { [orgId: string]: number } };
  mappingResults: RoleMapping[];
}

interface UserRole {
  user_id: number;
  nv_role_id: number;
  organization_id: string;
  mo_role_id: number | null;
}

interface ShiftData {
  shift_id: number;
  current_nv_role_id: number;
  shift_user_id: number | null;
  organization_id: string;
  user_mo_role_id: number | null;
  current_role_name: string;
}

interface MigrationPlan {
  shiftId: number;
  organizationId: string;
  oldNvRoleId: number;
  newMoRoleId: number;
  reason: string;
}

interface MigrationStats {
  totalProcessed: number;
  successful: number;
  failed: number;
}

// Using existing sequelize connection from models

/**
 * Create role mapping between nv_roles and mo_roles
 * @returns Role mapping object and results
 */
async function createRoleMapping(): Promise<RoleMappingResult> {
  console.log('🔄 Creating role mapping between nv_roles and mo_roles...');
  
  const roleMappingQuery = `
    SELECT 
      nr.id as nv_role_id,
      nr.role_name,
      mr.id as mo_role_id,
      mr.organization_id
    FROM nv_roles nr
    INNER JOIN mo_roles mr ON nr.role_name = mr.role_name
    WHERE mr.role_status = 'active'
    ORDER BY nr.id, mr.organization_id
  `;
  
  const mappingResults = await sequelize.query(roleMappingQuery, {
    type: QueryTypes.SELECT
  }) as RoleMapping[];
  
  console.log(`✅ Found ${mappingResults.length} role mappings`);
  
  // Create mapping object for quick lookup
  const roleMapping: { [nvRoleId: number]: { [orgId: string]: number } } = {};
  mappingResults.forEach(row => {
    if (!roleMapping[row.nv_role_id]) {
      roleMapping[row.nv_role_id] = {};
    }
    roleMapping[row.nv_role_id][row.organization_id] = row.mo_role_id;
  });
  
  return { roleMapping, mappingResults };
}

/**
 * Get user role information from user_role table and user_role_id
 * @returns User role information
 */
async function getUserRoleInfo(): Promise<UserRole[]> {
  console.log('🔄 Gathering user role information...');
  
  // Get user roles from user_role table (old system)
  const userRolesQuery = `
    SELECT 
      ur.user_id,
      ur.role_id as nv_role_id,
      u.organization_id,
      u.user_role_id as mo_role_id
    FROM nv_user_roles ur
    INNER JOIN nv_users u ON ur.user_id = u.id
      WHERE u.user_status NOT IN ('deleted', 'cancelled')
    ORDER BY ur.user_id, ur.role_id
  `;
  
  const userRoles = await sequelize.query(userRolesQuery, {
    type: QueryTypes.SELECT
  }) as UserRole[];
  
  console.log(`✅ Found ${userRoles.length} user role assignments`);
  
  return userRoles;
}

/**
 * Analyze shifts that need migration
 * @returns Shifts requiring migration
 */
async function analyzeShifts(): Promise<ShiftData[]> {
  console.log('🔄 Analyzing shifts requiring migration...');
  
  const shiftsAnalysisQuery = `
    SELECT 
      s.id as shift_id,
      s.roleId as current_nv_role_id,
      s.userId as shift_user_id,
      s.organization_id,
      u.user_role_id as user_mo_role_id,
      nr.role_name as current_role_name
    FROM shifts s
    LEFT JOIN nv_users u ON s.userId = u.id
    LEFT JOIN nv_roles nr ON s.roleId = nr.id
    WHERE s.status != 'deleted'
      AND s.roleId IS NOT NULL
      AND s.organization_id IS NOT NULL
    ORDER BY s.organization_id, s.id
  `;
  
  const shifts = await sequelize.query(shiftsAnalysisQuery, {
    type: QueryTypes.SELECT
  }) as ShiftData[];
  
  console.log(`✅ Found ${shifts.length} shifts requiring analysis`);
  
  return shifts;
}

/**
 * Determine the correct mo_role_id for a shift
 * @param shift - Shift data
 * @param roleMapping - Role mapping object
 * @param userRoles - User role assignments
 * @returns mo_role_id to use
 */
function determineCorrectMoRoleId(
  shift: ShiftData, 
  roleMapping: { [nvRoleId: number]: { [orgId: string]: number } }, 
  userRoles: UserRole[]
): number | null {
  const { current_nv_role_id, shift_user_id, organization_id, user_mo_role_id } = shift;
  
  // Get mo_role_id from role mapping
  const mappedMoRoleId = roleMapping[current_nv_role_id]?.[organization_id];
  
  if (!mappedMoRoleId) {
    console.warn(`⚠️  No mo_role mapping found for nv_role_id ${current_nv_role_id} in org ${organization_id}`);
    return null;
  }
  
  // If shift has no user (open shift), use mapped role
  if (!shift_user_id) {
    return mappedMoRoleId;
  }
  
  // Check if user has this role in user_role table
  const userHasRole = userRoles.some(ur => 
    ur.user_id === shift_user_id && 
    ur.nv_role_id === current_nv_role_id &&
    ur.organization_id === organization_id
  );
  
  if (userHasRole) {
    // User has this role, use mapped mo_role_id
    return mappedMoRoleId;
  } else {
    // User doesn't have this role, use their user_role_id instead
    if (user_mo_role_id) {
      console.log(`📝 Using user_role_id ${user_mo_role_id} for shift ${shift.shift_id} instead of mapped role`);
      return user_mo_role_id;
    } else {
      console.warn(`⚠️  User ${shift_user_id} has no user_role_id, using mapped role ${mappedMoRoleId}`);
      return mappedMoRoleId;
    }
  }
}

/**
 * Perform the migration in batches
 * @param migrationPlan - Array of migration operations
 */
async function performMigration(migrationPlan: MigrationPlan[]): Promise<MigrationStats> {
  console.log(`🔄 Starting migration of ${migrationPlan.length} shifts...`);
  
  const batchSize = 1000;
  let successCount = 0;
  let errorCount = 0;
  
  for (let i = 0; i < migrationPlan.length; i += batchSize) {
    const batch = migrationPlan.slice(i, i + batchSize);
    
    console.log(`📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(migrationPlan.length / batchSize)} (${batch.length} shifts)`);
    
    const transaction = await sequelize.transaction();
    
    try {
      for (const migration of batch) {
        const updateQuery = `
          UPDATE shifts 
          SET roleId = :newMoRoleId
          WHERE id = :shiftId 
            AND organization_id = :organizationId
        `;
        
        await sequelize.query(updateQuery, {
          replacements: {
            newMoRoleId: migration.newMoRoleId,
            shiftId: migration.shiftId,
            organizationId: migration.organizationId
          },
          type: QueryTypes.UPDATE,
          transaction
        });
        
        successCount++;
      }
      
      await transaction.commit();
      console.log(`✅ Batch completed successfully`);
      
    } catch (error) {
      await transaction.rollback();
      console.error(`❌ Batch failed:`, (error as Error).message);
      errorCount += batch.length;
    }
  }
  
  console.log(`\n📊 Migration Summary:`);
  console.log(`✅ Successfully migrated: ${successCount} shifts`);
  console.log(`❌ Failed migrations: ${errorCount} shifts`);
  
  return {
    totalProcessed: migrationPlan.length,
    successful: successCount,
    failed: errorCount
  };
}

/**
 * Validate migration results
 */
async function validateMigration(): Promise<void> {
  console.log('🔄 Validating migration results...');

  // Check for shifts still using nv_roles
  const remainingNvRolesQuery = `
    SELECT COUNT(*) as count
    FROM shifts s
    LEFT JOIN mo_roles mr ON s.roleId = mr.id AND s.organization_id = mr.organization_id
    WHERE s.status != 'deleted'
      AND s.roleId IS NOT NULL
      AND mr.id IS NULL
  `;

  const remainingResult = await sequelize.query(remainingNvRolesQuery, {
    type: QueryTypes.SELECT
  }) as [{ count: number }];

  const remainingCount = remainingResult[0].count;

  if (remainingCount > 0) {
    console.warn(`⚠️  ${remainingCount} shifts still reference non-existent mo_roles`);
  } else {
    console.log(`✅ All shifts now reference valid mo_roles`);
  }

  // Check migration statistics
  const statsQuery = `
    SELECT
      COUNT(*) as total_shifts,
      COUNT(DISTINCT s.roleId) as unique_roles,
      COUNT(DISTINCT s.organization_id) as organizations
    FROM shifts s
    INNER JOIN mo_roles mr ON s.roleId = mr.id AND s.organization_id = mr.organization_id
    WHERE s.status != 'deleted'
      AND s.roleId IS NOT NULL
  `;

  const stats = await sequelize.query(statsQuery, {
    type: QueryTypes.SELECT
  }) as [{ total_shifts: number; unique_roles: number; organizations: number }];

  console.log(`📊 Final Statistics:`);
  console.log(`   Total migrated shifts: ${stats[0].total_shifts}`);
  console.log(`   Unique mo_roles used: ${stats[0].unique_roles}`);
  console.log(`   Organizations affected: ${stats[0].organizations}`);
}

/**
 * Main migration function
 */
async function main(): Promise<void> {
  try {
    console.log('🚀 Starting Shift Role Migration to mo_roles...\n');
    console.log('✅ Using existing database connection\n');

    // Step 1: Create role mapping
    const { roleMapping } = await createRoleMapping();

    // Step 2: Get user role information
    const userRoles = await getUserRoleInfo();

    // Step 3: Analyze shifts
    const shifts = await analyzeShifts();

    // Step 4: Create migration plan
    console.log('🔄 Creating migration plan...');
    const migrationPlan: MigrationPlan[] = [];

    for (const shift of shifts) {
      const newMoRoleId = determineCorrectMoRoleId(shift, roleMapping, userRoles);

      if (newMoRoleId && newMoRoleId !== shift.current_nv_role_id) {
        migrationPlan.push({
          shiftId: shift.shift_id,
          organizationId: shift.organization_id,
          oldNvRoleId: shift.current_nv_role_id,
          newMoRoleId: newMoRoleId,
          reason: shift.shift_user_id ? 'user_role_validation' : 'role_mapping'
        });
      }
    }

    console.log(`✅ Migration plan created: ${migrationPlan.length} shifts to migrate\n`);

    if (migrationPlan.length === 0) {
      console.log('🎉 No shifts require migration. All shifts are already using correct mo_roles!');
      return;
    }

    // Step 5: Perform migration
    await performMigration(migrationPlan);

    // Step 6: Validate results
    await validateMigration();

    console.log('\n🎉 Migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', (error as Error).message);
    process.exit(1);
  }
}


  setTimeout(() => {
    console.log('🚀 Starting migration after database sync...\n');
    main().catch(console.error);
  }, 20000); // 20 seconds delay

export {
  main,
  createRoleMapping,
  getUserRoleInfo,
  analyzeShifts,
  determineCorrectMoRoleId,
  performMigration,
  validateMigration
};
