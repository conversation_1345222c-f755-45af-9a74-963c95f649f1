import { Workbook } from "exceljs";
import { json2csv } from "json-2-csv";
import puppeteer from "puppeteer";

export const generateFile = async (file_type: string, data?: any) => {
    try {
        switch (file_type) {
            case "pdf": {
                const browser = await puppeteer.launch({ args: ['--no-sandbox', '--disable-setuid-sandbox'] });
                const page = await browser.newPage();
                await page.setContent(data);
                const pdfBuffer = await page.pdf({ format: 'a4', printBackground: true, scale: 0.5 });
                await browser.close();
                return pdfBuffer;
            }
            case "excel": {
                return new Workbook();
            }
            case "csv": {
                const csvData = json2csv(data, { escapeHeaderNestedDots: false });
                const csvDataWithBOM = '\ufeff' + csvData;
                return Buffer.from(csvDataWithBOM, 'utf-8');
            }
            default: {
                throw new Error(`Unsupported file type: ${file_type}`);
            }
        }
    } catch (error) {
        console.error("Error generating file:", error);
        throw error; // Re-throw the error instead of returning null
    }
};


